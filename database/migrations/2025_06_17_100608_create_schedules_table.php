<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSchedulesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('schedules', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('class_id'); // ID lớp học
            $table->integer('day_of_week'); // Thứ trong tuần (1=Thứ 2, 7=Chủ nhật)
            $table->time('start_time'); // Giờ bắt đầu
            $table->time('end_time'); // Giờ kết thúc
            $table->string('room')->nullable(); // Phòng học
            $table->string('building')->nullable(); // Tòa nhà
            $table->enum('schedule_type', ['theory', 'practice', 'exam'])->default('theory'); // <PERSON>ại lịch
            $table->date('effective_from'); // <PERSON><PERSON> hiệu lực từ ngày
            $table->date('effective_to')->nullable(); // <PERSON><PERSON> hiệu lực đến ngày
            $table->boolean('is_active')->default(true); // Có hoạt động không
            $table->text('notes')->nullable(); // Ghi chú
            $table->timestamps();

            $table->foreign('class_id')->references('id')->on('student_classes')->onDelete('cascade');
            $table->index(['class_id', 'day_of_week', 'is_active']);
            $table->index(['day_of_week', 'start_time', 'end_time']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('schedules');
    }
}
