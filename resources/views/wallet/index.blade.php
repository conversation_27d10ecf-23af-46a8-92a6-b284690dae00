@extends('layouts.modern')

@section('title', '<PERSON><PERSON> điện tử')
@section('page-title', '<PERSON><PERSON> điện tử')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">V<PERSON> điện tử</h1>
                <p class="text-gray-600 dark:text-gray-400 mt-1">Quản lý số dư và giao dịch của bạn</p>
            </div>
            <div class="flex items-center space-x-3">
                <button class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors">
                    <i class="fas fa-plus mr-2"></i>
                    N<PERSON><PERSON> tiền
                </button>
                <button class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors">
                    <i class="fas fa-download mr-2"></i>
                    Xuất sao kê
                </button>
            </div>
        </div>
    </div>

    <!-- Wallet Balance -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-xl shadow-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-green-100 text-sm font-medium">Số dư hiện tại</p>
                    <p class="text-3xl font-bold">2,450,000 VNĐ</p>
                </div>
                <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                    <i class="fas fa-wallet text-2xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl shadow-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-blue-100 text-sm font-medium">Tổng nạp tháng này</p>
                    <p class="text-3xl font-bold">1,500,000 VNĐ</p>
                </div>
                <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                    <i class="fas fa-arrow-up text-2xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl shadow-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-purple-100 text-sm font-medium">Tổng chi tháng này</p>
                    <p class="text-3xl font-bold">950,000 VNĐ</p>
                </div>
                <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                    <i class="fas fa-arrow-down text-2xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Thao tác nhanh</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button class="flex flex-col items-center p-6 bg-green-50 dark:bg-green-900/20 rounded-xl border border-green-200 dark:border-green-800 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                    <div class="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center mb-3">
                        <i class="fas fa-credit-card text-white text-xl"></i>
                    </div>
                    <span class="text-sm font-medium text-green-700 dark:text-green-300">Nạp tiền</span>
                </button>

                <button class="flex flex-col items-center p-6 bg-blue-50 dark:bg-blue-900/20 rounded-xl border border-blue-200 dark:border-blue-800 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                    <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mb-3">
                        <i class="fas fa-exchange-alt text-white text-xl"></i>
                    </div>
                    <span class="text-sm font-medium text-blue-700 dark:text-blue-300">Chuyển tiền</span>
                </button>

                <button class="flex flex-col items-center p-6 bg-purple-50 dark:bg-purple-900/20 rounded-xl border border-purple-200 dark:border-purple-800 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                    <div class="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mb-3">
                        <i class="fas fa-receipt text-white text-xl"></i>
                    </div>
                    <span class="text-sm font-medium text-purple-700 dark:text-purple-300">Thanh toán</span>
                </button>

                <button class="flex flex-col items-center p-6 bg-orange-50 dark:bg-orange-900/20 rounded-xl border border-orange-200 dark:border-orange-800 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                    <div class="w-12 h-12 bg-orange-600 rounded-lg flex items-center justify-center mb-3">
                        <i class="fas fa-history text-white text-xl"></i>
                    </div>
                    <span class="text-sm font-medium text-orange-700 dark:text-orange-300">Lịch sử</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Recent Transactions -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Giao dịch gần đây</h3>
                <button class="text-blue-600 hover:text-blue-700 dark:text-blue-400 text-sm font-medium">
                    Xem tất cả →
                </button>
            </div>
        </div>
        <div class="divide-y divide-gray-200 dark:divide-gray-700">
            @php
            $transactions = [
                ['type' => 'in', 'title' => 'Nạp tiền từ ngân hàng', 'amount' => 500000, 'date' => '2023-12-18 14:30', 'status' => 'success'],
                ['type' => 'out', 'title' => 'Thanh toán học phí', 'amount' => 2500000, 'date' => '2023-12-17 09:15', 'status' => 'success'],
                ['type' => 'out', 'title' => 'Mua sách giáo khoa', 'amount' => 150000, 'date' => '2023-12-16 16:45', 'status' => 'success'],
                ['type' => 'in', 'title' => 'Hoàn tiền từ cửa hàng', 'amount' => 75000, 'date' => '2023-12-15 11:20', 'status' => 'success'],
                ['type' => 'out', 'title' => 'Thanh toán ăn uống', 'amount' => 45000, 'date' => '2023-12-14 12:30', 'status' => 'success'],
            ];
            @endphp

            @foreach($transactions as $transaction)
            <div class="p-6 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 rounded-lg flex items-center justify-center
                                {{ $transaction['type'] == 'in' ? 'bg-green-100 dark:bg-green-900' : 'bg-red-100 dark:bg-red-900' }}">
                                <i class="fas {{ $transaction['type'] == 'in' ? 'fa-arrow-down text-green-600 dark:text-green-400' : 'fa-arrow-up text-red-600 dark:text-red-400' }}"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="font-medium text-gray-900 dark:text-white">{{ $transaction['title'] }}</p>
                            <p class="text-sm text-gray-500 dark:text-gray-400">{{ $transaction['date'] }}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="font-bold {{ $transaction['type'] == 'in' ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                            {{ $transaction['type'] == 'in' ? '+' : '-' }}{{ number_format($transaction['amount']) }} VNĐ
                        </p>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                            Thành công
                        </span>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>

    <!-- Payment Methods -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Phương thức thanh toán</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                    <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-4">
                        <i class="fab fa-cc-visa text-blue-600 dark:text-blue-400 text-xl"></i>
                    </div>
                    <div>
                        <p class="font-medium text-gray-900 dark:text-white">Visa **** 1234</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Hết hạn 12/25</p>
                    </div>
                </div>

                <div class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                    <div class="w-12 h-12 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center mr-4">
                        <i class="fab fa-cc-mastercard text-red-600 dark:text-red-400 text-xl"></i>
                    </div>
                    <div>
                        <p class="font-medium text-gray-900 dark:text-white">MasterCard **** 5678</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Hết hạn 08/26</p>
                    </div>
                </div>

                <button class="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg hover:border-blue-500 dark:hover:border-blue-400 transition-colors">
                    <div class="text-center">
                        <i class="fas fa-plus text-gray-400 text-xl mb-2"></i>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Thêm thẻ mới</p>
                    </div>
                </button>
            </div>
        </div>
    </div>
</div>
@endsection
