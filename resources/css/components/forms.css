/* Form Component Styles */

/* Form Groups */
.form-group {
    margin-bottom: 24px;
    position: relative;
}

.form-group:last-child {
    margin-bottom: 0;
}

/* Form Labels */
.form-label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
    font-size: 0.9rem;
    letter-spacing: 0.025em;
}

.form-label i {
    margin-right: 6px;
    opacity: 0.8;
}

/* Input Groups */
.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-icon {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    font-size: 1rem;
    z-index: 3;
    transition: color 0.3s ease;
}

/* Form Controls */
.form-control {
    width: 100%;
    padding: 16px 16px 16px 48px;
    background: var(--input-bg);
    border: 1px solid var(--input-border);
    border-radius: 12px;
    color: var(--text-primary);
    font-size: 1rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    position: relative;
    z-index: 2;
}

.form-control.with-icon {
    padding-left: 48px;
}

.form-control::placeholder {
    color: var(--text-secondary);
    opacity: 0.7;
}

.form-control:focus {
    outline: none;
    border-color: rgba(102, 126, 234, 0.5);
    box-shadow: 
        0 0 0 3px rgba(102, 126, 234, 0.1),
        0 4px 12px rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-1px);
}

.form-control:focus + .input-icon,
.form-control:focus ~ .input-icon {
    color: #667eea;
}

/* Validation States */
.form-control.is-valid {
    border-color: #28a745;
    background: rgba(40, 167, 69, 0.1);
}

.form-control.is-invalid {
    border-color: #dc3545;
    background: rgba(220, 53, 69, 0.1);
}

.invalid-feedback {
    display: block;
    font-size: 0.875rem;
    margin-top: 6px;
    color: #ff6b6b;
}

.valid-feedback {
    display: block;
    font-size: 0.875rem;
    margin-top: 6px;
    color: #28a745;
}

/* Checkboxes and Radio Buttons */
.form-check {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}

.form-check-input {
    width: 18px;
    height: 18px;
    margin-right: 8px;
    background: var(--input-bg);
    border: 1px solid var(--input-border);
    border-radius: 4px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.form-check-input:checked {
    background: var(--accent-gradient);
    border-color: #ff6b6b;
}

.form-check-input:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.2);
}

.form-check-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    cursor: pointer;
    user-select: none;
}

/* Password Toggle */
.password-toggle {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.3s ease;
    z-index: 4;
}

.password-toggle:hover {
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.1);
}

.password-toggle:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.3);
}

/* Password Strength Indicator */
.password-strength {
    margin-top: 12px;
}

.password-strength .progress {
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 8px;
}

.password-strength .progress-bar {
    height: 100%;
    border-radius: 3px;
    transition: all 0.3s ease;
    background: linear-gradient(90deg, #ff6b6b, #ff8e8e);
}

.password-strength-text {
    font-size: 0.8rem;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: 6px;
}

.password-match-text {
    font-size: 0.8rem;
    margin-top: 6px;
    display: flex;
    align-items: center;
    gap: 4px;
}

/* Select Dropdowns */
.form-select {
    width: 100%;
    padding: 16px 48px 16px 16px;
    background: var(--input-bg);
    border: 1px solid var(--input-border);
    border-radius: 12px;
    color: var(--text-primary);
    font-size: 1rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 16px center;
    background-repeat: no-repeat;
    background-size: 16px 12px;
}

.form-select:focus {
    outline: none;
    border-color: rgba(102, 126, 234, 0.5);
    box-shadow: 
        0 0 0 3px rgba(102, 126, 234, 0.1),
        0 4px 12px rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 0.08);
}

/* Textarea */
.form-textarea {
    width: 100%;
    padding: 16px;
    background: var(--input-bg);
    border: 1px solid var(--input-border);
    border-radius: 12px;
    color: var(--text-primary);
    font-size: 1rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    resize: vertical;
    min-height: 120px;
    font-family: inherit;
}

.form-textarea:focus {
    outline: none;
    border-color: rgba(102, 126, 234, 0.5);
    box-shadow: 
        0 0 0 3px rgba(102, 126, 234, 0.1),
        0 4px 12px rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 0.08);
}

.form-textarea::placeholder {
    color: var(--text-secondary);
    opacity: 0.7;
}

/* File Upload */
.form-file {
    position: relative;
    display: inline-block;
    width: 100%;
}

.form-file-input {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.form-file-label {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px;
    background: var(--input-bg);
    border: 2px dashed var(--input-border);
    border-radius: 12px;
    color: var(--text-secondary);
    font-size: 1rem;
    transition: all 0.3s ease;
    cursor: pointer;
    min-height: 120px;
    text-align: center;
}

.form-file-label:hover {
    border-color: rgba(102, 126, 234, 0.5);
    background: rgba(255, 255, 255, 0.05);
}

.form-file-label i {
    font-size: 2rem;
    margin-bottom: 8px;
    display: block;
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 12px;
    margin-top: 24px;
}

.form-actions .btn {
    flex: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
    .form-control,
    .form-select,
    .form-textarea {
        padding: 14px 14px 14px 44px;
        font-size: 16px; /* Prevents zoom on iOS */
    }
    
    .form-control.with-icon {
        padding-left: 44px;
    }
    
    .input-icon {
        left: 14px;
        font-size: 0.9rem;
    }
    
    .password-toggle {
        right: 14px;
    }
    
    .form-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-control,
    .form-select,
    .form-textarea {
        padding: 12px 12px 12px 40px;
        border-radius: 10px;
    }
    
    .input-icon {
        left: 12px;
    }
    
    .password-toggle {
        right: 12px;
    }
}
