require('./bootstrap');

// Import Alpine.js
import Alpine from 'alpinejs'

// Alpine.js stores
Alpine.store('theme', {
    dark: localStorage.getItem('theme') === 'dark' || (!localStorage.getItem('theme') && window.matchMedia('(prefers-color-scheme: dark)').matches),

    init() {
        this.updateTheme();
        // Listen for system theme changes
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
            if (!localStorage.getItem('theme')) {
                this.dark = e.matches;
                this.updateTheme();
            }
        });
    },

    toggle() {
        this.dark = !this.dark;
        this.updateTheme();

        // Add a nice animation effect
        document.documentElement.style.transition = 'background-color 0.3s ease, color 0.3s ease';
        setTimeout(() => {
            document.documentElement.style.transition = '';
        }, 300);
    },

    setTheme(isDark) {
        this.dark = isDark;
        this.updateTheme();
    },

    updateTheme() {
        if (this.dark) {
            document.documentElement.classList.add('dark');
            localStorage.setItem('theme', 'dark');
        } else {
            document.documentElement.classList.remove('dark');
            localStorage.setItem('theme', 'light');
        }
    }
});

Alpine.data('sidebar', () => ({
    open: false,

    toggle() {
        this.open = !this.open;
    },

    close() {
        this.open = false;
    }
}));

Alpine.data('dropdown', () => ({
    open: false,

    toggle() {
        this.open = !this.open;
    },

    close() {
        this.open = false;
    }
}));

Alpine.data('modal', () => ({
    open: false,

    show() {
        this.open = true;
        document.body.style.overflow = 'hidden';
    },

    hide() {
        this.open = false;
        document.body.style.overflow = 'auto';
    }
}));

Alpine.data('notification', () => ({
    show: false,
    message: '',
    type: 'info',

    notify(message, type = 'info') {
        this.message = message;
        this.type = type;
        this.show = true;

        setTimeout(() => {
            this.show = false;
        }, 5000);
    }
}));

// Start Alpine.js
Alpine.start();

// Make Alpine available globally
window.Alpine = Alpine;

// Import component scripts
require('./components/auth');
require('./components/app');
