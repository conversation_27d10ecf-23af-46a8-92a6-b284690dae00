@extends('layouts.modern')

@section('title', '<PERSON><PERSON><PERSON><PERSON> số')
@section('page-title', '<PERSON><PERSON><PERSON><PERSON> số')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white"><PERSON><PERSON><PERSON><PERSON> số</h1>
                <p class="text-gray-600 dark:text-gray-400 mt-1">Xem điểm số và kết quả học tập của bạn</p>
            </div>
            <div class="flex items-center space-x-3">
                <select class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    <option><PERSON><PERSON><PERSON> kỳ 1 - 2023/2024</option>
                    <option><PERSON><PERSON><PERSON> kỳ 2 - 2022/2023</option>
                    <option><PERSON><PERSON><PERSON> kỳ 1 - 2022/2023</option>
                </select>
                <button class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors">
                    <i class="fas fa-download mr-2"></i>
                    Xuất bảng điểm
                </button>
            </div>
        </div>
    </div>

    <!-- GPA Overview -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl shadow-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-blue-100 text-sm font-medium">GPA Học kỳ</p>
                    <p class="text-3xl font-bold">3.45</p>
                </div>
                <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                    <i class="fas fa-chart-line text-2xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-xl shadow-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-green-100 text-sm font-medium">GPA Tích lũy</p>
                    <p class="text-3xl font-bold">3.28</p>
                </div>
                <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                    <i class="fas fa-trophy text-2xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl shadow-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-purple-100 text-sm font-medium">Tín chỉ đạt</p>
                    <p class="text-3xl font-bold">45</p>
                </div>
                <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                    <i class="fas fa-certificate text-2xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-r from-orange-500 to-orange-600 rounded-xl shadow-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-orange-100 text-sm font-medium">Xếp hạng</p>
                    <p class="text-3xl font-bold">15/120</p>
                </div>
                <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                    <i class="fas fa-medal text-2xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Grades Table -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Bảng điểm chi tiết</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Môn học</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Mã môn</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Tín chỉ</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Điểm QT</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Điểm CK</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Điểm TK</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Điểm chữ</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Trạng thái</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                    @php
                    $subjects = [
                        ['name' => 'Lập trình Web', 'code' => 'IT001', 'credits' => 3, 'qt' => 8.5, 'ck' => 7.8, 'tk' => 8.0, 'letter' => 'B+', 'status' => 'Đạt'],
                        ['name' => 'Cơ sở dữ liệu', 'code' => 'IT002', 'credits' => 3, 'qt' => 9.0, 'ck' => 8.5, 'tk' => 8.7, 'letter' => 'A', 'status' => 'Đạt'],
                        ['name' => 'Mạng máy tính', 'code' => 'IT003', 'credits' => 3, 'qt' => 7.5, 'ck' => 6.8, 'tk' => 7.1, 'letter' => 'B', 'status' => 'Đạt'],
                        ['name' => 'Hệ điều hành', 'code' => 'IT004', 'credits' => 3, 'qt' => 8.8, 'ck' => 9.2, 'tk' => 9.0, 'letter' => 'A', 'status' => 'Đạt'],
                        ['name' => 'Phân tích thiết kế hệ thống', 'code' => 'IT005', 'credits' => 3, 'qt' => 6.5, 'ck' => 5.8, 'tk' => 6.1, 'letter' => 'C+', 'status' => 'Đạt'],
                    ];
                    @endphp
                    
                    @foreach($subjects as $subject)
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td class="px-6 py-4">
                            <div class="font-medium text-gray-900 dark:text-white">{{ $subject['name'] }}</div>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">{{ $subject['code'] }}</td>
                        <td class="px-6 py-4 text-sm text-gray-900 dark:text-white font-medium">{{ $subject['credits'] }}</td>
                        <td class="px-6 py-4 text-sm text-gray-900 dark:text-white">{{ $subject['qt'] }}</td>
                        <td class="px-6 py-4 text-sm text-gray-900 dark:text-white">{{ $subject['ck'] }}</td>
                        <td class="px-6 py-4">
                            <span class="text-lg font-bold 
                                @if($subject['tk'] >= 8.5) text-green-600 dark:text-green-400
                                @elseif($subject['tk'] >= 7.0) text-blue-600 dark:text-blue-400
                                @elseif($subject['tk'] >= 5.5) text-yellow-600 dark:text-yellow-400
                                @else text-red-600 dark:text-red-400
                                @endif">
                                {{ $subject['tk'] }}
                            </span>
                        </td>
                        <td class="px-6 py-4">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                @if($subject['letter'] == 'A') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                @elseif(in_array($subject['letter'], ['B+', 'B'])) bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                                @elseif(in_array($subject['letter'], ['C+', 'C'])) bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                                @else bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200
                                @endif">
                                {{ $subject['letter'] }}
                            </span>
                        </td>
                        <td class="px-6 py-4">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                {{ $subject['status'] }}
                            </span>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>

    <!-- Grade Chart -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Biểu đồ điểm số</h3>
        </div>
        <div class="p-6">
            <div class="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="text-center">
                    <i class="fas fa-chart-bar text-4xl text-gray-400 mb-4"></i>
                    <p class="text-gray-500 dark:text-gray-400">Biểu đồ điểm số sẽ được hiển thị ở đây</p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
