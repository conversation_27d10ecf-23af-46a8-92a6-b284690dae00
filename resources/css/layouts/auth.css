/* Authentication Layout Styles */

/* CSS Variables */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --modern-gradient: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    --accent-gradient: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    --success-gradient: linear-gradient(135deg, #00d2ff 0%, #3a7bd5 100%);
    --dark-gradient: linear-gradient(135deg, #0c0c0c 0%, #1a1a1a 100%);
    --glass-bg: rgba(30, 30, 30, 0.85);
    --glass-border: rgba(255, 255, 255, 0.1);
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --input-bg: rgba(255, 255, 255, 0.05);
    --input-border: rgba(255, 255, 255, 0.15);
    --shadow-soft: 0 15px 35px rgba(0, 0, 0, 0.3);
    --shadow-hover: 0 25px 50px rgba(0, 0, 0, 0.4);
}

/* Light Mode Variables */
[data-theme="light"] {
    --modern-gradient: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    --glass-bg: rgba(255, 255, 255, 0.25);
    --glass-border: rgba(255, 255, 255, 0.3);
    --text-primary: #2d3748;
    --text-secondary: #4a5568;
    --input-bg: rgba(255, 255, 255, 0.8);
    --input-border: rgba(0, 0, 0, 0.1);
    --shadow-soft: 0 15px 35px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 25px 50px rgba(0, 0, 0, 0.15);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Cross-browser normalization */
html {
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    text-size-adjust: 100%;
}

body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    font-family: 'Inter', sans-serif;
    background: var(--modern-gradient);
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
    transition: all 0.5s ease;
    color-profile: sRGB;
    rendering-intent: perceptual;
}

/* Light mode body adjustments */
[data-theme="light"] body {
    background: var(--modern-gradient);
}

/* Background Effects */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(255, 107, 107, 0.06) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(0, 210, 255, 0.06) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(102, 126, 234, 0.06) 0%, transparent 50%),
        radial-gradient(circle at 60% 60%, rgba(238, 90, 36, 0.04) 0%, transparent 50%);
    background:
        -webkit-radial-gradient(circle at 20% 80%, rgba(255, 107, 107, 0.06) 0%, transparent 50%),
        -webkit-radial-gradient(circle at 80% 20%, rgba(0, 210, 255, 0.06) 0%, transparent 50%),
        -webkit-radial-gradient(circle at 40% 40%, rgba(102, 126, 234, 0.06) 0%, transparent 50%),
        -webkit-radial-gradient(circle at 60% 60%, rgba(238, 90, 36, 0.04) 0%, transparent 50%);
    z-index: -2;
    animation: backgroundShift 20s ease-in-out infinite;
}

/* Main Container */
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    position: relative;
    z-index: 1;
}

/* Glass Card */
.glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(25px);
    border-radius: 20px;
    border: 1px solid var(--glass-border);
    box-shadow: var(--shadow-soft);
    padding: 40px 32px;
    width: 100%;
    max-width: 420px;
    min-height: 580px;
    transition: all 0.4s ease;
    animation: slideInUp 0.8s ease-out;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.glass-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

.glass-card:hover {
    transform: translateY(-8px);
    box-shadow:
        var(--shadow-hover),
        0 0 50px rgba(255, 107, 107, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Auth Header */
.auth-header {
    text-align: center;
    margin-bottom: 32px;
}

.auth-logo {
    width: 80px;
    height: 80px;
    background: var(--accent-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
    animation: logoFloat 3s ease-in-out infinite;
}

.auth-logo i {
    font-size: 2rem;
    color: white;
}

.auth-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 8px;
    letter-spacing: -0.025em;
}

.auth-subtitle {
    color: var(--text-secondary);
    font-size: 0.95rem;
    line-height: 1.5;
    margin-bottom: 0;
}

/* Theme Toggle */
.theme-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 50px;
    padding: 8px 16px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
}

.theme-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.05);
}

.theme-toggle i {
    color: var(--text-primary);
    font-size: 1.1rem;
}

/* Fix auto scroll issue */
html, body {
    overflow-x: hidden;
    scroll-behavior: auto;
    height: 100%;
}

body {
    position: relative;
    min-height: 100vh;
    max-width: 100vw;
}

/* Prevent layout shift and auto scroll */
.auth-container {
    position: relative;
    z-index: 10;
    width: 100%;
    max-width: 100vw;
}

/* Ensure floating elements don't cause scroll */
.floating-elements,
.tech-elements {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
    overflow: hidden;
}

.floating-element,
.tech-element {
    position: absolute;
    pointer-events: none;
    will-change: transform;
}

/* Particle system fix */
.particle {
    position: absolute;
    pointer-events: none;
    will-change: transform;
}

/* Floating Elements */
.floating-elements {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    overflow: hidden;
}

.tech-elements {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    overflow: hidden;
}

.tech-element {
    position: absolute;
    opacity: 0.06;
    color: rgba(0, 210, 255, 0.7);
    font-family: 'Courier New', monospace;
    font-weight: bold;
    animation: techFloat 10s ease-in-out infinite;
}

.code-brackets {
    top: 25%;
    left: 15%;
    font-size: 2.5rem;
    animation-delay: 1s;
}

.binary-code {
    top: 60%;
    right: 15%;
    font-size: 1.5rem;
    animation-delay: 3s;
    letter-spacing: 2px;
}

.wifi-signal {
    top: 40%;
    left: 5%;
    font-size: 2rem;
    animation-delay: 5s;
}

.cpu-chip {
    bottom: 30%;
    right: 8%;
    font-size: 2rem;
    animation-delay: 7s;
}

/* Academic Floating Elements */
.floating-element {
    position: absolute;
    opacity: 0.06;
    animation: academicFloat 15s ease-in-out infinite;
    color: rgba(255, 107, 107, 0.4);
    filter: blur(0.3px);
}

.book-icon {
    top: 20%;
    left: 10%;
    font-size: 3rem;
    animation-delay: 2s;
}

.graduation-cap {
    top: 70%;
    right: 20%;
    font-size: 2.5rem;
    animation-delay: 6s;
}

.pencil-icon {
    bottom: 25%;
    left: 25%;
    font-size: 2rem;
    animation-delay: 10s;
}

.calculator-icon {
    top: 45%;
    right: 10%;
    font-size: 2.2rem;
    animation-delay: 14s;
}

.atom-icon {
    bottom: 60%;
    left: 5%;
    font-size: 2.8rem;
    animation-delay: 4s;
}

.globe-icon {
    top: 35%;
    left: 80%;
    font-size: 2.3rem;
    animation-delay: 8s;
}

.microscope-icon {
    bottom: 40%;
    right: 5%;
    font-size: 2.1rem;
    animation-delay: 12s;
}

/* Particle System */
.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(255, 107, 107, 0.6);
    border-radius: 50%;
    pointer-events: none;
    animation: particleFloat 8s linear infinite;
}

.particle:nth-child(2n) {
    background: rgba(0, 210, 255, 0.6);
    animation-duration: 10s;
}

.particle:nth-child(3n) {
    background: rgba(102, 126, 234, 0.6);
    animation-duration: 12s;
}

.particle:nth-child(4n) {
    width: 2px;
    height: 2px;
    background: rgba(255, 255, 255, 0.4);
    animation-duration: 15s;
}

/* Responsive Design */
@media (max-width: 768px) {
    .auth-container {
        padding: 15px;
    }
    
    .glass-card {
        padding: 30px 24px;
        min-height: 500px;
        max-width: 100%;
    }
    
    .auth-title {
        font-size: 1.5rem;
    }
    
    .auth-logo {
        width: 70px;
        height: 70px;
    }
    
    .auth-logo i {
        font-size: 1.75rem;
    }
    
    .theme-toggle {
        top: 15px;
        right: 15px;
        padding: 6px 12px;
    }
    
    .floating-element,
    .tech-element {
        font-size: 1.5rem !important;
    }
}

@media (max-width: 480px) {
    .glass-card {
        padding: 25px 20px;
        border-radius: 15px;
    }
    
    .auth-title {
        font-size: 1.35rem;
    }
    
    .auth-subtitle {
        font-size: 0.9rem;
    }
}
