<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\WalletController;
use App\Http\Controllers\GradeController;
use App\Http\Controllers\ScheduleController;
use App\Http\Controllers\StudentController;

// Public routes
Route::get('/', function () {
    return redirect()->route('login');
});

// Authentication Routes (manual definition for better control)
Route::get('login', [App\Http\Controllers\Auth\LoginController::class, 'showLoginForm'])->name('login');
Route::post('login', [App\Http\Controllers\Auth\LoginController::class, 'login']);
Route::post('logout', [App\Http\Controllers\Auth\LoginController::class, 'logout'])->name('logout');

// Password Reset Routes
Route::get('password/reset', [App\Http\Controllers\Auth\ForgotPasswordController::class, 'showLinkRequestForm'])->name('password.request');
Route::post('password/email', [App\Http\Controllers\Auth\ForgotPasswordController::class, 'sendResetLinkEmail'])->name('password.email');
Route::get('password/reset/{token}', [App\Http\Controllers\Auth\ResetPasswordController::class, 'showResetForm'])->name('password.reset');
Route::post('password/reset', [App\Http\Controllers\Auth\ResetPasswordController::class, 'reset'])->name('password.update');

// Google OAuth Routes
Route::get('/auth/google', [App\Http\Controllers\Auth\GoogleController::class, 'redirectToGoogle'])->name('auth.google');
Route::get('/auth/google/callback', [App\Http\Controllers\Auth\GoogleController::class, 'handleGoogleCallback'])->name('auth.google.callback');

// Student Portal Routes (Protected)
Route::middleware(['auth'])->group(function () {
    
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard/data', [DashboardController::class, 'getData'])->name('dashboard.data');

    // Announcements
    Route::prefix('announcements')->name('announcements.')->group(function () {
        Route::get('/', function () {
            return view('announcements.index');
        })->name('index');
    });
    
    // Student Profile
    Route::prefix('profile')->name('profile.')->group(function () {
        Route::get('/', [StudentController::class, 'profile'])->name('index');
        Route::get('/edit', [StudentController::class, 'editProfile'])->name('edit');
        Route::put('/update', [StudentController::class, 'updateProfile'])->name('update');
    });
    
    // Classes & Courses
    Route::prefix('classes')->name('classes.')->group(function () {
        Route::get('/', [StudentController::class, 'classes'])->name('index');
        Route::get('/{id}', [StudentController::class, 'classDetail'])->name('detail');
    });
    
    // Schedule
    Route::prefix('schedule')->name('schedule.')->group(function () {
        Route::get('/', [ScheduleController::class, 'index'])->name('index');
        Route::get('/calendar', [ScheduleController::class, 'calendar'])->name('calendar');
    });
    
    // Grades
    Route::prefix('grades')->name('grades.')->group(function () {
        Route::get('/', [GradeController::class, 'index'])->name('index');
        Route::get('/transcript', [GradeController::class, 'transcript'])->name('transcript');
    });
    
    // Wallet & Financial
    Route::prefix('wallet')->name('wallet.')->group(function () {
        Route::get('/', [WalletController::class, 'index'])->name('index');
        Route::get('/topup', [WalletController::class, 'topup'])->name('topup');
        Route::get('/history', [WalletController::class, 'history'])->name('history');
    });
    
});
