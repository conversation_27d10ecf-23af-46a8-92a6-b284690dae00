<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Laravel\Socialite\Facades\Socialite;
use Illuminate\Support\Str;

class GoogleController extends Controller
{
    /**
     * Redirect to Google OAuth
     */
    public function redirectToGoogle()
    {
        return Socialite::driver('google')->redirect();
    }

    /**
     * Handle Google OAuth callback
     */
    public function handleGoogleCallback()
    {
        try {
            $googleUser = Socialite::driver('google')->user();
            
            // Check if user already exists
            $user = User::where('email', $googleUser->getEmail())->first();
            
            if ($user) {
                // Update user info if needed
                $user->update([
                    'google_id' => $googleUser->getId(),
                    'avatar' => $googleUser->getAvatar(),
                ]);
            } else {
                // Create new user
                $user = User::create([
                    'name' => $googleUser->getName(),
                    'full_name' => $googleUser->getName(),
                    'email' => $googleUser->getEmail(),
                    'google_id' => $googleUser->getId(),
                    'avatar' => $googleUser->getAvatar(),
                    'user_code' => $this->generateUserCode(),
                    'password' => Hash::make(Str::random(24)), // Random password
                    'user_level' => 3, // Default to student
                    'email_verified_at' => now(),
                ]);
            }
            
            // Login user
            Auth::login($user, true);
            
            return redirect()->intended('/dashboard')->with('success', 'Đăng nhập Google thành công!');
            
        } catch (\Exception $e) {
            return redirect('/login')->with('error', 'Có lỗi xảy ra khi đăng nhập với Google. Vui lòng thử lại.');
        }
    }

    /**
     * Generate unique user code
     */
    private function generateUserCode()
    {
        do {
            $code = 'STU' . str_pad(rand(0, 999999), 6, '0', STR_PAD_LEFT);
        } while (User::where('user_code', $code)->exists());
        
        return $code;
    }
}
