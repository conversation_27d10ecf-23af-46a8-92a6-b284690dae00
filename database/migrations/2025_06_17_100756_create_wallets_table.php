<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWalletsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('wallets', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->unique(); // ID sinh viên
            $table->decimal('balance', 15, 2)->default(0); // Số dư ví
            $table->decimal('pending_balance', 15, 2)->default(0); // Số dư đang chờ xử lý
            $table->string('wallet_code', 20)->unique(); // Mã ví
            $table->enum('status', ['active', 'suspended', 'closed'])->default('active');
            $table->string('pin_code', 255)->nullable(); // Mã PIN (encrypted)
            $table->timestamp('last_transaction_at')->nullable(); // Giao dịch cuối cùng
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('user')->onDelete('cascade');
            $table->index(['user_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('wallets');
    }
}
