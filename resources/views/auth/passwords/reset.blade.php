@extends('layouts.auth')

@section('title', 'Đặt lại mật khẩu')

@section('content')
<div class="auth-header">
    <div class="auth-logo">
        <i class="fas fa-lock"></i>
    </div>
    <h1 class="auth-title">Đặt lại mật khẩu</h1>
    <p class="auth-subtitle"><PERSON><PERSON><PERSON><PERSON> mật khẩu mới để hoàn tất việc đặt lại</p>
</div>

@if ($errors->any())
    <div class="alert alert-danger animate__animated animate__shakeX">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <ul class="mb-0 ps-3">
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif

<form method="POST" action="{{ route('password.update') }}" id="resetPasswordForm">
    @csrf
    <input type="hidden" name="token" value="{{ $token }}">
    
    <div class="form-group">
        <label for="email" class="form-label">
            <i class="fas fa-envelope me-2"></i>Địa chỉ Email
        </label>
        <div class="input-group">
            <i class="fas fa-envelope input-icon"></i>
            <input 
                id="email" 
                type="email" 
                class="form-control with-icon @error('email') is-invalid @enderror" 
                name="email" 
                value="{{ $email ?? old('email') }}" 
                required 
                autocomplete="email" 
                autofocus
                placeholder="Nhập địa chỉ email"
            >
        </div>
        @error('email')
            <div class="invalid-feedback d-block text-white-50 mt-1">
                <i class="fas fa-exclamation-circle me-1"></i>{{ $message }}
            </div>
        @enderror
    </div>

    <div class="form-group">
        <label for="password" class="form-label">
            <i class="fas fa-lock me-2"></i>Mật khẩu mới
        </label>
        <div class="input-group">
            <i class="fas fa-lock input-icon"></i>
            <input 
                id="password" 
                type="password" 
                class="form-control with-icon @error('password') is-invalid @enderror" 
                name="password" 
                required 
                autocomplete="new-password"
                placeholder="Nhập mật khẩu mới (tối thiểu 8 ký tự)"
            >
            <button
                type="button"
                class="password-toggle"
                onclick="togglePassword('password')"
            >
                <i class="fas fa-eye" id="togglePasswordIcon"></i>
            </button>
        </div>
        <div class="password-strength mt-2" id="passwordStrength">
            <div class="progress">
                <div class="progress-bar" role="progressbar"></div>
            </div>
            <small class="password-strength-text mt-1 d-block" id="passwordStrengthText">Độ mạnh mật khẩu</small>
        </div>
        @error('password')
            <div class="invalid-feedback d-block text-white-50 mt-1">
                <i class="fas fa-exclamation-circle me-1"></i>{{ $message }}
            </div>
        @enderror
    </div>

    <div class="form-group">
        <label for="password-confirm" class="form-label">
            <i class="fas fa-lock me-2"></i>Xác nhận mật khẩu mới
        </label>
        <div class="input-group">
            <i class="fas fa-lock input-icon"></i>
            <input 
                id="password-confirm" 
                type="password" 
                class="form-control with-icon" 
                name="password_confirmation" 
                required 
                autocomplete="new-password"
                placeholder="Nhập lại mật khẩu mới"
            >
            <button
                type="button"
                class="password-toggle"
                onclick="togglePassword('password-confirm')"
            >
                <i class="fas fa-eye" id="togglePasswordConfirmIcon"></i>
            </button>
        </div>
        <div class="password-match-text mt-2" id="passwordMatch">
            <small id="passwordMatchText"></small>
        </div>
    </div>

    <button type="submit" class="btn btn-primary" id="resetBtn">
        <i class="fas fa-save me-2"></i>
        Đặt lại mật khẩu
    </button>
</form>

<div class="auth-links">
    <div class="text-center">
        <a href="{{ route('login') }}">
            <i class="fas fa-arrow-left me-1"></i>
            Quay lại đăng nhập
        </a>
    </div>
</div>


@endsection
