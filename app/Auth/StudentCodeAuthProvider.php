<?php

namespace App\Auth;

use Illuminate\Auth\EloquentUserProvider;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Support\Str;

class StudentCodeAuthProvider extends EloquentUserProvider
{
    /**
     * Validate a user against the given credentials.
     *
     * @param  \Illuminate\Contracts\Auth\Authenticatable  $user
     * @param  array  $credentials
     * @return bool
     */
    public function validateCredentials(Authenticatable $user, array $credentials)
    {
        $password = $credentials['password'] ?? '';
        $userPassword = $user->getAuthPassword();

        // If user has a password in database, validate it
        if (!empty($userPassword)) {
            return $this->hasher->check($password, $userPassword);
        }

        // If user password is empty, use default password "123@123"
        if (empty($userPassword)) {
            return $password === '123@123';
        }

        return false;
    }

    /**
     * Retrieve a user by the given credentials.
     *
     * @param  array  $credentials
     * @return \Illuminate\Contracts\Auth\Authenticatable|null
     */
    public function retrieveByCredentials(array $credentials)
    {
        if (empty($credentials) || 
            (count($credentials) === 1 && array_key_exists('password', $credentials))) {
            return null;
        }

        // Build the query
        $query = $this->newModelQuery();

        foreach ($credentials as $key => $value) {
            if (Str::contains($key, 'password')) {
                continue;
            }

            if (is_array($value) || $value instanceof \Closure) {
                $query->where($key, $value);
            } else {
                $query->where($key, $value);
            }
        }

        return $query->first();
    }
}
