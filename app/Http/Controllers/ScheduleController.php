<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ScheduleController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index()
    {
        $student = Auth::user();
        $schedules = collect(); // TODO: Implement schedule retrieval
        return view('schedule.index', compact('student', 'schedules'));
    }

    public function calendar()
    {
        $student = Auth::user();
        $events = collect(); // TODO: Implement calendar events
        return view('schedule.calendar', compact('student', 'events'));
    }
}
