<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class StudentController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function profile()
    {
        $student = Auth::user();
        return view('profile.index', compact('student'));
    }

    public function editProfile()
    {
        $student = Auth::user();
        return view('profile.edit', compact('student'));
    }

    public function updateProfile(Request $request)
    {
        // TODO: Implement profile update
        return redirect()->route('profile.index')->with('success', 'Cập nhật thông tin thành công!');
    }

    public function classes()
    {
        $student = Auth::user();
        $classes = collect(); // TODO: Implement classes retrieval
        return view('classes.index', compact('student', 'classes'));
    }

    public function classDetail($id)
    {
        $student = Auth::user();
        // TODO: Implement class detail retrieval
        return view('classes.detail', compact('student', 'id'));
    }
}
