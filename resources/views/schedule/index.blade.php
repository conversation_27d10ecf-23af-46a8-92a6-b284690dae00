@extends('layouts.modern')

@section('title', '<PERSON><PERSON><PERSON> học')
@section('page-title', '<PERSON><PERSON><PERSON> học')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Lịch học</h1>
                <p class="text-gray-600 dark:text-gray-400 mt-1">Xem lịch học và thời khóa biểu của bạn</p>
            </div>
            <div class="flex items-center space-x-3">
                <button class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-lg transition-colors">
                    <i class="fas fa-calendar-week mr-2"></i>
                    Tuần này
                </button>
                <button class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors">
                    <i class="fas fa-download mr-2"></i>
                    Xuất lịch
                </button>
            </div>
        </div>
    </div>

    <!-- Calendar View -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Tuần từ 18/12 - 24/12/2023</h2>
                <div class="flex items-center space-x-2">
                    <button class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button class="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-lg">Hôm nay</button>
                    <button class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Calendar Grid -->
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-20">Giờ</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Thứ 2</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Thứ 3</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Thứ 4</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Thứ 5</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Thứ 6</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Thứ 7</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                    @for($hour = 7; $hour <= 17; $hour++)
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td class="px-4 py-6 text-sm text-gray-500 dark:text-gray-400 font-medium">
                            {{ sprintf('%02d:00', $hour) }}
                        </td>
                        @for($day = 1; $day <= 6; $day++)
                        <td class="px-4 py-6 relative">
                            @if(($hour == 8 && $day == 2) || ($hour == 10 && $day == 4) || ($hour == 14 && $day == 1))
                            <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-3 rounded-lg shadow-md">
                                <div class="font-semibold text-sm">Lập trình Web</div>
                                <div class="text-xs opacity-90">Phòng A301</div>
                                <div class="text-xs opacity-90">GV. Nguyễn Văn A</div>
                            </div>
                            @elseif(($hour == 9 && $day == 3) || ($hour == 15 && $day == 5))
                            <div class="bg-gradient-to-r from-green-500 to-teal-600 text-white p-3 rounded-lg shadow-md">
                                <div class="font-semibold text-sm">Cơ sở dữ liệu</div>
                                <div class="text-xs opacity-90">Phòng B201</div>
                                <div class="text-xs opacity-90">GV. Trần Thị B</div>
                            </div>
                            @endif
                        </td>
                        @endfor
                    </tr>
                    @endfor
                </tbody>
            </table>
        </div>
    </div>

    <!-- Today's Schedule -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-calendar-day text-white text-sm"></i>
                </div>
                Lịch học hôm nay
            </h3>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                <div class="flex items-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-xl border border-blue-200 dark:border-blue-800">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                            <i class="fas fa-clock text-white"></i>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="font-bold text-gray-900 dark:text-white text-lg">Lập trình Web</p>
                                <p class="text-sm text-gray-600 dark:text-gray-400">8:00 - 10:30 • Phòng A301</p>
                                <p class="text-sm text-gray-500 dark:text-gray-500">GV. Nguyễn Văn A</p>
                            </div>
                            <div class="text-right">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                    Sắp diễn ra
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex items-center p-4 bg-green-50 dark:bg-green-900/20 rounded-xl border border-green-200 dark:border-green-800">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center">
                            <i class="fas fa-clock text-white"></i>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="font-bold text-gray-900 dark:text-white text-lg">Cơ sở dữ liệu</p>
                                <p class="text-sm text-gray-600 dark:text-gray-400">14:00 - 16:30 • Phòng B201</p>
                                <p class="text-sm text-gray-500 dark:text-gray-500">GV. Trần Thị B</p>
                            </div>
                            <div class="text-right">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                    Chiều nay
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
