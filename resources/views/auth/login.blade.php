<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="h-full">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Đăng nhập - {{ config('app.name', 'Student Portal') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

    <!-- Custom CSS -->
    <link href="{{ mix('css/app.css') }}" rel="stylesheet">
</head>
<body class="h-full bg-gradient-to-br from-primary-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900" x-data="{ showPassword: false }">
    <div class="min-h-full flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <!-- Header -->
            <div class="text-center">
                <div class="mx-auto h-20 w-20 bg-gradient-to-r from-primary-600 to-purple-600 rounded-full flex items-center justify-center mb-6">
                    <i class="fas fa-graduation-cap text-3xl text-white"></i>
                </div>
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white">
                    Đăng nhập
                </h2>
                <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                    Chào mừng bạn đến với Student Portal
                </p>
            </div>

            <!-- Login Form -->
<div class="auth-header">
    <div class="auth-logo">
        <i class="fas fa-graduation-cap"></i>
    </div>
    <h1 class="auth-title">Chào mừng trở lại!</h1>
    <p class="auth-subtitle">Đăng nhập vào Student Portal</p>
</div>

@if ($errors->any())
    <div class="alert alert-danger animate__animated animate__shakeX">
        <i class="fas fa-exclamation-triangle me-2"></i>
        @foreach ($errors->all() as $error)
            {{ $error }}
        @endforeach
    </div>
@endif

@if (session('status'))
    <div class="alert alert-success animate__animated animate__fadeInDown">
        <i class="fas fa-check-circle me-2"></i>
        {{ session('status') }}
    </div>
@endif

<form method="POST" action="{{ route('login') }}" id="loginForm">
    @csrf

    <div class="form-group">
        <div class="input-group">
            <i class="fas fa-user input-icon"></i>
            <input
                id="login"
                type="text"
                class="form-control with-icon @error('login') is-invalid @enderror"
                name="login"
                value="{{ old('login') }}"
                required
                autocomplete="username"
                autofocus
                placeholder="Email hoặc mã sinh viên"
            >
        </div>
        @error('login')
            <div class="invalid-feedback d-block text-white-50 mt-1">
                <i class="fas fa-exclamation-circle me-1"></i>{{ $message }}
            </div>
        @enderror
    </div>

    <div class="form-group">
        <div class="input-group">
            <i class="fas fa-lock input-icon"></i>
            <input
                id="password"
                type="password"
                class="form-control with-icon @error('password') is-invalid @enderror"
                name="password"
                required
                autocomplete="current-password"
                placeholder="Mật khẩu"
            >
            <button
                type="button"
                class="password-toggle"
                onclick="togglePassword()"
                id="togglePasswordBtn"
            >
                <i class="fas fa-eye" id="togglePasswordIcon"></i>
            </button>
        </div>
        @error('password')
            <div class="invalid-feedback d-block text-white-50 mt-1">
                <i class="fas fa-exclamation-circle me-1"></i>{{ $message }}
            </div>
        @enderror
    </div>

    <div class="form-group">
        <div class="form-check d-flex align-items-center justify-content-between">
            <div class="d-flex align-items-center">
                <input
                    class="form-check-input me-2"
                    type="checkbox"
                    name="remember"
                    id="remember"
                    {{ old('remember') ? 'checked' : '' }}
                >
                <label class="form-check-label text-white-50" for="remember">
                    Ghi nhớ
                </label>
            </div>
            @if (Route::has('password.request'))
                <a href="{{ route('password.request') }}" class="auth-link">
                    Quên mật khẩu?
                </a>
            @endif
        </div>
    </div>

    <button type="submit" class="btn btn-primary w-100" id="loginBtn">
        <i class="fas fa-sign-in-alt me-2"></i>
        Đăng nhập
    </button>
</form>

<!-- Divider -->
<div class="auth-divider">
    <div class="divider-line"></div>
    <span class="divider-text">hoặc</span>
    <div class="divider-line"></div>
</div>

<!-- Google Login -->
<button type="button" class="btn btn-google w-100" onclick="alert('Tính năng đăng nhập Google sẽ được triển khai sau!')">
    <div class="google-icon">
        <svg width="20" height="20" viewBox="0 0 24 24">
            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
        </svg>
    </div>
    <span>Đăng nhập với Google</span>
</button>

<!-- Test Accounts Info -->
<div class="test-accounts-info mt-4">
    <div class="card bg-dark border-secondary">
        <div class="card-header">
            <h6 class="mb-0 text-white">
                <i class="fas fa-info-circle me-2"></i>
                Tài khoản test
            </h6>
        </div>
        <div class="card-body">
            <small class="text-white-50">
                <strong>Sinh viên có sẵn:</strong><br>
                Email: <EMAIL> | Mã SV: **********<br>
                Email: <EMAIL> | Mã SV: **********<br>
                Email: <EMAIL> | Mã SV: **********<br>
                <strong>Mật khẩu mặc định:</strong> 123@123<br>
                <small class="text-muted">(Cho các tài khoản chưa thiết lập mật khẩu)</small>
            </small>
        </div>
    </div>
</div>

@if (Route::has('register'))
    <div class="auth-links">
        <span class="text-secondary">Chưa có tài khoản? </span>
        <a href="{{ route('register') }}" class="auth-link">
            Đăng ký ngay
        </a>
    </div>
@endif

@endsection
