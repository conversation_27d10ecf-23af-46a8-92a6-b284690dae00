@extends('layouts.modern')

@section('title', '<PERSON>ớ<PERSON> học')
@section('page-title', '<PERSON><PERSON><PERSON> học của tôi')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Lớ<PERSON> học của tôi</h1>
                <p class="text-gray-600 dark:text-gray-400 mt-1">Quản lý và theo dõi các lớp học bạn đang tham gia</p>
            </div>
            <div class="flex items-center space-x-3">
                <button class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors">
                    <i class="fas fa-plus mr-2"></i>
                    <PERSON><PERSON><PERSON> ký lớp mới
                </button>
            </div>
        </div>
    </div>

    <!-- Classes Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        @for($i = 1; $i <= 6; $i++)
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
            <div class="bg-gradient-to-r from-blue-500 to-purple-600 p-4">
                <h3 class="text-white font-bold text-lg">Lập trình Web {{ $i }}</h3>
                <p class="text-blue-100 text-sm">IT{{ str_pad($i, 3, '0', STR_PAD_LEFT) }}</p>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                        <i class="fas fa-user-tie w-4 mr-2"></i>
                        <span>GV. Nguyễn Văn A</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                        <i class="fas fa-calendar w-4 mr-2"></i>
                        <span>Thứ {{ $i + 1 }}, 7:00 - 9:30</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                        <i class="fas fa-map-marker-alt w-4 mr-2"></i>
                        <span>Phòng A{{ $i }}01</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                        <i class="fas fa-users w-4 mr-2"></i>
                        <span>{{ 25 + $i }}/40 sinh viên</span>
                    </div>
                </div>
                <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <div class="flex items-center justify-between">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                            Đang học
                        </span>
                        <button class="text-blue-600 hover:text-blue-700 dark:text-blue-400 text-sm font-medium">
                            Xem chi tiết →
                        </button>
                    </div>
                </div>
            </div>
        </div>
        @endfor
    </div>

    <!-- Empty State (hidden when there are classes) -->
    <div class="hidden bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-12 text-center">
        <div class="w-20 h-20 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-chalkboard-teacher text-3xl text-gray-400"></i>
        </div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Chưa có lớp học nào</h3>
        <p class="text-gray-500 dark:text-gray-400 mb-6">Bạn chưa đăng ký lớp học nào. Hãy đăng ký lớp học để bắt đầu học tập.</p>
        <button class="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors">
            <i class="fas fa-plus mr-2"></i>
            Đăng ký lớp học
        </button>
    </div>
</div>
@endsection
