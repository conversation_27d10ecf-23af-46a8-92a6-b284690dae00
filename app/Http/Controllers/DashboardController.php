<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\StudentWallet;
use App\Models\WalletTransaction;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the student dashboard.
     */
    public function index()
    {
        $student = Auth::user();

        // Get current term classes (placeholder for now)
        $currentClasses = collect();

        // Get today's schedule (placeholder for now)
        $todaySchedule = collect();

        // Get wallet information (placeholder for now)
        $wallet = null;
        $walletBalance = 0;

        // Get recent transactions (placeholder for now)
        $recentTransactions = collect();

        // Get recent grades (placeholder for now)
        $recentGrades = collect();

        // Get notifications/announcements (placeholder for now)
        $notifications = collect();

        // Statistics (placeholder data)
        $stats = [
            'total_classes' => 0,
            'active_classes' => 0,
            'completed_classes' => 0,
            'wallet_balance' => 0,
            'total_credits' => 0,
        ];

        return view('dashboard.index', compact(
            'student',
            'currentClasses',
            'todaySchedule',
            'wallet',
            'recentTransactions',
            'recentGrades',
            'notifications',
            'stats'
        ));
    }

    /**
     * Get dashboard data as JSON (for AJAX requests)
     */
    public function getData()
    {
        $student = Auth::user();

        $data = [
            'student' => [
                'name' => $student->display_name,
                'code' => $student->user_code,
                'email' => $student->user_email,
            ],
            'classes_count' => 0, // TODO: Implement when class system is ready
            'wallet_balance' => 0, // TODO: Implement when wallet system is ready
            'today_schedule_count' => 0, // TODO: Implement when schedule system is ready
        ];

        return response()->json($data);
    }
}
