@extends('layouts.modern')

@section('title', 'Bảng tin')
@section('page-title', 'Bảng tin')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Bảng tin</h1>
                <p class="text-gray-600 dark:text-gray-400 mt-1">Thông báo và tin tức từ nhà trường</p>
            </div>
            <div class="flex items-center space-x-3">
                <div class="flex items-center space-x-2">
                    <span class="text-sm text-gray-500 dark:text-gray-400">Lọc theo:</span>
                    <select class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm">
                        <option>Tất cả</option>
                        <option><PERSON><PERSON><PERSON> vụ</option>
                        <option>Tài ch<PERSON>h</option>
                        <option>Sự kiện</option>
                        <option>Khẩn cấp</option>
                    </select>
                </div>
                <button class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors">
                    <i class="fas fa-bell mr-2"></i>
                    Đánh dấu đã đọc
                </button>
            </div>
        </div>
    </div>

    <!-- Important Announcements -->
    <div class="bg-gradient-to-r from-red-500 to-pink-600 rounded-xl shadow-lg p-6 text-white">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
                    <i class="fas fa-exclamation-triangle text-2xl"></i>
                </div>
            </div>
            <div class="ml-4 flex-1">
                <h3 class="text-lg font-bold mb-2">Thông báo khẩn cấp</h3>
                <p class="text-white/90 mb-3">
                    Thay đổi lịch thi cuối kỳ học kỳ 1 năm học 2023-2024. Sinh viên vui lòng kiểm tra lịch thi mới.
                </p>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-white/80">
                        <i class="fas fa-clock mr-1"></i>
                        2 giờ trước
                    </span>
                    <button class="px-4 py-2 bg-white/20 hover:bg-white/30 rounded-lg text-sm font-medium transition-colors">
                        Xem chi tiết
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Announcements List -->
    <div class="space-y-4">
        @php
        $announcements = [
            [
                'id' => 1,
                'title' => 'Thông báo về việc đóng học phí học kỳ 2',
                'content' => 'Sinh viên cần hoàn thành việc đóng học phí trước ngày 15/01/2024. Các trường hợp đặc biệt vui lòng liên hệ phòng Tài chính.',
                'type' => 'finance',
                'priority' => 'high',
                'created_at' => '2023-12-18 10:30:00',
                'is_read' => false,
                'author' => 'Phòng Tài chính',
                'views' => 1250
            ],
            [
                'id' => 2,
                'title' => 'Lịch nghỉ Tết Nguyên đán 2024',
                'content' => 'Trường thông báo lịch nghỉ Tết Nguyên đán từ ngày 08/02 đến 17/02/2024. Sinh viên sẽ trở lại học vào ngày 19/02/2024.',
                'type' => 'academic',
                'priority' => 'medium',
                'created_at' => '2023-12-17 14:15:00',
                'is_read' => true,
                'author' => 'Phòng Đào tạo',
                'views' => 2100
            ],
            [
                'id' => 3,
                'title' => 'Hội thảo "Xu hướng công nghệ 2024"',
                'content' => 'Khoa Công nghệ thông tin tổ chức hội thảo về xu hướng công nghệ mới. Thời gian: 9h00 ngày 22/12/2023 tại Hội trường A.',
                'type' => 'event',
                'priority' => 'medium',
                'created_at' => '2023-12-16 09:00:00',
                'is_read' => false,
                'author' => 'Khoa CNTT',
                'views' => 850
            ],
            [
                'id' => 4,
                'title' => 'Cập nhật quy định về trang phục trong trường',
                'content' => 'Từ ngày 01/01/2024, sinh viên cần tuân thủ quy định mới về trang phục khi tham gia các hoạt động học tập.',
                'type' => 'regulation',
                'priority' => 'low',
                'created_at' => '2023-12-15 16:45:00',
                'is_read' => true,
                'author' => 'Phòng Công tác sinh viên',
                'views' => 1680
            ],
            [
                'id' => 5,
                'title' => 'Thông báo tuyển sinh viên tham gia dự án nghiên cứu',
                'content' => 'Khoa CNTT tuyển 10 sinh viên xuất sắc tham gia dự án nghiên cứu về AI. Hạn nộp hồ sơ: 30/12/2023.',
                'type' => 'opportunity',
                'priority' => 'high',
                'created_at' => '2023-12-14 11:20:00',
                'is_read' => false,
                'author' => 'Khoa CNTT',
                'views' => 920
            ]
        ];

        $typeColors = [
            'finance' => ['bg' => 'bg-yellow-100 dark:bg-yellow-900/20', 'text' => 'text-yellow-800 dark:text-yellow-200', 'icon' => 'fas fa-dollar-sign'],
            'academic' => ['bg' => 'bg-blue-100 dark:bg-blue-900/20', 'text' => 'text-blue-800 dark:text-blue-200', 'icon' => 'fas fa-graduation-cap'],
            'event' => ['bg' => 'bg-purple-100 dark:bg-purple-900/20', 'text' => 'text-purple-800 dark:text-purple-200', 'icon' => 'fas fa-calendar-alt'],
            'regulation' => ['bg' => 'bg-gray-100 dark:bg-gray-900/20', 'text' => 'text-gray-800 dark:text-gray-200', 'icon' => 'fas fa-gavel'],
            'opportunity' => ['bg' => 'bg-green-100 dark:bg-green-900/20', 'text' => 'text-green-800 dark:text-green-200', 'icon' => 'fas fa-star']
        ];

        $priorityColors = [
            'high' => 'border-l-red-500',
            'medium' => 'border-l-yellow-500',
            'low' => 'border-l-green-500'
        ];
        @endphp

        @foreach($announcements as $announcement)
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 border-l-4 {{ $priorityColors[$announcement['priority']] }} hover:shadow-xl transition-all duration-300 {{ !$announcement['is_read'] ? 'ring-2 ring-blue-500/20' : '' }}">
            <div class="p-6">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <div class="flex items-center space-x-3 mb-3">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $typeColors[$announcement['type']]['bg'] }} {{ $typeColors[$announcement['type']]['text'] }}">
                                <i class="{{ $typeColors[$announcement['type']]['icon'] }} mr-1"></i>
                                {{ ucfirst($announcement['type']) }}
                            </span>
                            
                            @if(!$announcement['is_read'])
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                    <i class="fas fa-circle text-blue-500 text-xs mr-1"></i>
                                    Mới
                                </span>
                            @endif
                            
                            @if($announcement['priority'] === 'high')
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                    <i class="fas fa-exclamation text-red-500 text-xs mr-1"></i>
                                    Quan trọng
                                </span>
                            @endif
                        </div>
                        
                        <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-2 {{ !$announcement['is_read'] ? 'text-blue-600 dark:text-blue-400' : '' }}">
                            {{ $announcement['title'] }}
                        </h3>
                        
                        <p class="text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">
                            {{ $announcement['content'] }}
                        </p>
                        
                        <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                            <div class="flex items-center space-x-4">
                                <span class="flex items-center">
                                    <i class="fas fa-user mr-1"></i>
                                    {{ $announcement['author'] }}
                                </span>
                                <span class="flex items-center">
                                    <i class="fas fa-clock mr-1"></i>
                                    {{ \Carbon\Carbon::parse($announcement['created_at'])->diffForHumans() }}
                                </span>
                                <span class="flex items-center">
                                    <i class="fas fa-eye mr-1"></i>
                                    {{ number_format($announcement['views']) }} lượt xem
                                </span>
                            </div>
                            
                            <div class="flex items-center space-x-2">
                                @if(!$announcement['is_read'])
                                    <button class="px-3 py-1 bg-blue-100 hover:bg-blue-200 text-blue-700 text-xs font-medium rounded-lg transition-colors">
                                        Đánh dấu đã đọc
                                    </button>
                                @endif
                                <button class="px-3 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 text-xs font-medium rounded-lg transition-colors">
                                    Xem chi tiết
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endforeach
    </div>

    <!-- Pagination -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between">
            <div class="text-sm text-gray-500 dark:text-gray-400">
                Hiển thị <span class="font-medium">1-5</span> trong <span class="font-medium">25</span> thông báo
            </div>
            <div class="flex items-center space-x-2">
                <button class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    Trước
                </button>
                <button class="px-3 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium">1</button>
                <button class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">2</button>
                <button class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">3</button>
                <button class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    Sau
                </button>
            </div>
        </div>
    </div>
</div>
@endsection
