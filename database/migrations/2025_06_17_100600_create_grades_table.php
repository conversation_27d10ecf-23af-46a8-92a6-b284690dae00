<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateGradesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('grades', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('enrollment_id'); // ID đăng ký lớp học
            $table->string('grade_type'); // Loại điểm: attendance, assignment, midterm, final, etc.
            $table->string('grade_name'); // Tên bài kiểm tra/bài tập
            $table->decimal('score', 4, 2); // Điểm số (0-10)
            $table->decimal('max_score', 4, 2)->default(10); // Điểm tối đa
            $table->decimal('weight', 3, 2)->default(1.00); // Tr<PERSON><PERSON> số (0-1)
            $table->date('exam_date')->nullable(); // Ngày thi/nộp bài
            $table->text('notes')->nullable(); // Ghi chú
            $table->timestamps();

            $table->foreign('enrollment_id')->references('id')->on('class_enrollments')->onDelete('cascade');
            $table->index(['enrollment_id', 'grade_type']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('grades');
    }
}
