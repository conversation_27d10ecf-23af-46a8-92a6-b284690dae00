<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    use HasFactory, Notifiable;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'user';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_login',
        'user_pass',
        'user_level',
        'user_surname',
        'user_middlename',
        'user_givenname',
        'user_code',
        'user_email',
        'user_address',
        'user_telephone',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'user_pass',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'user_DOB' => 'date',
        'ngaycap' => 'date',
        'created_date' => 'date',
        'learn_start_day' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the password for the user.
     */
    public function getAuthPassword()
    {
        return $this->user_pass;
    }

    /**
     * Get the name of the unique identifier for the user.
     */
    public function getAuthIdentifierName()
    {
        return 'id';
    }

    /**
     * Get the unique identifier for the user.
     */
    public function getAuthIdentifier()
    {
        return $this->getKey();
    }

    /**
     * Check if user is a student
     */
    public function isStudent()
    {
        return $this->user_level === 3;
    }

    /**
     * Check if user is a teacher
     */
    public function isTeacher()
    {
        return $this->user_level === 2;
    }

    /**
     * Check if user is an admin
     */
    public function isAdmin()
    {
        return $this->user_level === 1;
    }

    /**
     * Get user's display name
     */
    public function getDisplayNameAttribute()
    {
        return trim($this->user_surname . ' ' . $this->user_middlename . ' ' . $this->user_givenname);
    }

    /**
     * Get user's full name (alias for display name)
     */
    public function getNameAttribute()
    {
        return $this->getDisplayNameAttribute();
    }

    /**
     * Get user's email (alias for user_email)
     */
    public function getEmailAttribute()
    {
        return $this->user_email;
    }

    /**
     * Get user's level name
     */
    public function getUserLevelNameAttribute()
    {
        $levels = [
            1 => 'Quản trị viên',
            2 => 'Giảng viên',
            3 => 'Sinh viên'
        ];

        return $levels[$this->user_level] ?? 'Không xác định';
    }

    /**
     * Get user's full name (for compatibility)
     */
    public function getFullNameAttribute()
    {
        return $this->getDisplayNameAttribute();
    }

    /**
     * Get current term classes (placeholder - return empty collection for now)
     */
    public function currentTermClasses()
    {
        // TODO: Implement based on actual database structure
        // For now, return empty query builder to prevent errors
        return new class {
            public function with($relations) { return $this; }
            public function get() { return collect(); }
            public function count() { return 0; }
        };
    }

    /**
     * Get wallet relationship (placeholder)
     */
    public function getWalletAttribute()
    {
        // TODO: Implement wallet relationship
        return null;
    }

    /**
     * Get wallet balance (placeholder)
     */
    public function getWalletBalanceAttribute()
    {
        // TODO: Implement wallet balance
        return 0;
    }
}
