<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="h-full">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'Student Portal') - {{ config('app.name', 'Laravel') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

    <!-- Custom CSS -->
    <link href="{{ mix('css/app.css') }}" rel="stylesheet">
    
    @stack('styles')
</head>
<body class="h-full bg-education-modern dark:bg-gray-900" x-data="{ sidebarOpen: false }">
    <div class="layout-container">
        <!-- Mobile sidebar overlay -->
        <div x-show="sidebarOpen"
             x-transition:enter="transition-opacity ease-linear duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition-opacity ease-linear duration-300"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 z-40 lg:hidden">
            <div class="fixed inset-0 bg-gray-600 bg-opacity-75" @click="sidebarOpen = false"></div>
        </div>

        <!-- Sidebar -->
        <div class="layout-sidebar fixed inset-y-0 left-0 z-50 bg-white dark:bg-gray-800 shadow-xl border-r border-gray-100 dark:border-gray-700 transform transition-transform duration-300 ease-in-out lg:relative lg:translate-x-0 lg:flex lg:flex-col sidebar-scroll overflow-y-auto"
             :class="sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'">
            
            <!-- Sidebar header -->
            <div class="flex items-center justify-between h-20 px-6 bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 shadow-lg">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm">
                            <i class="fas fa-graduation-cap text-xl text-white"></i>
                        </div>
                    </div>
                    <div class="ml-3">
                        <h1 class="text-xl font-bold text-white">Student Portal</h1>
                        <p class="text-xs text-white/80">Hệ thống quản lý học tập</p>
                    </div>
                </div>
                <button @click="sidebarOpen = false" class="lg:hidden text-white hover:text-white/80 transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            @auth
            <!-- User info -->
            <div class="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 dark:bg-gray-900/50 border-b border-blue-100 dark:border-gray-700">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-14 h-14 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                            <i class="fas fa-user text-white text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-bold text-gray-900 dark:text-white">
                            {{ Auth::user()->display_name ?? Auth::user()->user_login }}
                        </p>
                        <p class="text-xs text-gray-500 dark:text-gray-400 font-medium">
                            {{ Auth::user()->user_code }}
                        </p>
                        <div class="mt-1">
                            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                <i class="fas fa-circle text-green-500 text-xs mr-1"></i>
                                Online
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            @endauth

            <!-- Navigation -->
            <nav class="mt-6 px-3">
                <div class="space-y-1">
                    <a href="{{ route('dashboard') }}"
                       class="nav-link {{ request()->routeIs('dashboard') ? 'nav-link-active' : 'nav-link-inactive' }}">
                        <i class="fas fa-home w-5 h-5 mr-3"></i>
                        Dashboard
                    </a>

                    <a href="{{ route('announcements.index') }}"
                       class="nav-link {{ request()->routeIs('announcements.*') ? 'nav-link-active' : 'nav-link-inactive' }} relative">
                        <i class="fas fa-bullhorn w-5 h-5 mr-3"></i>
                        Bảng tin
                        <span class="absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-bold">3</span>
                    </a>

                    <a href="{{ route('classes.index') }}"
                       class="nav-link {{ request()->routeIs('classes.*') ? 'nav-link-active' : 'nav-link-inactive' }}">
                        <i class="fas fa-chalkboard-teacher w-5 h-5 mr-3"></i>
                        Lớp học
                    </a>

                    <a href="{{ route('schedule.index') }}"
                       class="nav-link {{ request()->routeIs('schedule.*') ? 'nav-link-active' : 'nav-link-inactive' }}">
                        <i class="fas fa-calendar-alt w-5 h-5 mr-3"></i>
                        Lịch học
                    </a>

                    <a href="{{ route('grades.index') }}"
                       class="nav-link {{ request()->routeIs('grades.*') ? 'nav-link-active' : 'nav-link-inactive' }}">
                        <i class="fas fa-chart-line w-5 h-5 mr-3"></i>
                        Điểm số
                    </a>

                    <a href="{{ route('wallet.index') }}"
                       class="nav-link {{ request()->routeIs('wallet.*') ? 'nav-link-active' : 'nav-link-inactive' }}">
                        <i class="fas fa-wallet w-5 h-5 mr-3"></i>
                        Ví điện tử
                    </a>

                    <a href="{{ route('profile.index') }}"
                       class="nav-link {{ request()->routeIs('profile.*') ? 'nav-link-active' : 'nav-link-inactive' }}">
                        <i class="fas fa-user-cog w-5 h-5 mr-3"></i>
                        Hồ sơ
                    </a>
                </div>
            </nav>

            <!-- Bottom section -->
            <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-center">
                    <!-- Logout -->
                    <form method="POST" action="{{ route('logout') }}" class="w-full">
                        @csrf
                        <button type="submit"
                                class="w-full flex items-center justify-center px-4 py-3 text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-all duration-200 font-medium">
                            <i class="fas fa-sign-out-alt mr-2"></i>
                            Đăng xuất
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Main content -->
        <div class="layout-main">
            <!-- Top bar -->
            <div class="sticky top-0 z-40 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm">
                <div class="flex h-16 items-center gap-x-4 px-6">
                    <button @click="sidebarOpen = true" class="p-2 text-gray-700 dark:text-gray-200 lg:hidden hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
                        <i class="fas fa-bars text-xl"></i>
                    </button>

                    <!-- Separator -->
                    <div class="h-6 w-px bg-gray-200 dark:bg-gray-700 lg:hidden"></div>

                    <div class="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
                        <div class="relative flex flex-1 items-center">
                            <h1 class="text-xl font-semibold text-gray-900 dark:text-white">
                                @yield('page-title', 'Dashboard')
                            </h1>
                        </div>
                        
                        <div class="flex items-center gap-x-4 lg:gap-x-6">
                            <!-- Theme Toggle -->
                            <div class="theme-toggle"
                                 :class="$store.theme.dark ? 'dark' : ''"
                                 @click="$store.theme.toggle()"
                                 x-init="$store.theme.init()">
                                <div class="theme-toggle-slider">
                                    <i class="fas fa-sun theme-icon sun-icon"
                                       :class="$store.theme.dark ? 'opacity-0' : 'opacity-100'"></i>
                                    <i class="fas fa-moon theme-icon moon-icon absolute"
                                       :class="$store.theme.dark ? 'opacity-100' : 'opacity-0'"></i>
                                </div>
                            </div>

                            <!-- Notifications -->
                            <div class="relative" x-data="{ open: false }">
                                <button @click="open = !open" class="relative p-2 text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 transition-colors">
                                    <i class="fas fa-bell text-lg"></i>
                                    <span class="absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-bold animate-pulse">3</span>
                                </button>

                                <!-- Notification Dropdown -->
                                <div x-show="open"
                                     @click.away="open = false"
                                     x-transition:enter="transition ease-out duration-100"
                                     x-transition:enter-start="transform opacity-0 scale-95"
                                     x-transition:enter-end="transform opacity-100 scale-100"
                                     x-transition:leave="transition ease-in duration-75"
                                     x-transition:leave-start="transform opacity-100 scale-100"
                                     x-transition:leave-end="transform opacity-0 scale-95"
                                     class="absolute right-0 z-10 mt-2 w-80 origin-top-right rounded-xl bg-white dark:bg-gray-800 shadow-xl ring-1 ring-black ring-opacity-5 focus:outline-none border border-gray-200 dark:border-gray-700">

                                    <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                                        <div class="flex items-center justify-between">
                                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Thông báo</h3>
                                            <a href="{{ route('announcements.index') }}" class="text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400 font-medium">
                                                Xem tất cả
                                            </a>
                                        </div>
                                    </div>

                                    <div class="max-h-96 overflow-y-auto">
                                        <div class="p-2 space-y-1">
                                            <a href="#" class="block p-3 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors">
                                                <div class="flex items-start">
                                                    <div class="flex-shrink-0">
                                                        <div class="w-8 h-8 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                                                            <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400 text-sm"></i>
                                                        </div>
                                                    </div>
                                                    <div class="ml-3 flex-1">
                                                        <p class="text-sm font-medium text-gray-900 dark:text-white">Thay đổi lịch thi cuối kỳ</p>
                                                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">2 giờ trước</p>
                                                    </div>
                                                    <div class="flex-shrink-0">
                                                        <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                                                    </div>
                                                </div>
                                            </a>

                                            <a href="#" class="block p-3 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors">
                                                <div class="flex items-start">
                                                    <div class="flex-shrink-0">
                                                        <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900 rounded-full flex items-center justify-center">
                                                            <i class="fas fa-dollar-sign text-yellow-600 dark:text-yellow-400 text-sm"></i>
                                                        </div>
                                                    </div>
                                                    <div class="ml-3 flex-1">
                                                        <p class="text-sm font-medium text-gray-900 dark:text-white">Thông báo đóng học phí</p>
                                                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">1 ngày trước</p>
                                                    </div>
                                                    <div class="flex-shrink-0">
                                                        <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                                                    </div>
                                                </div>
                                            </a>

                                            <a href="#" class="block p-3 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors">
                                                <div class="flex items-start">
                                                    <div class="flex-shrink-0">
                                                        <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center">
                                                            <i class="fas fa-calendar-alt text-purple-600 dark:text-purple-400 text-sm"></i>
                                                        </div>
                                                    </div>
                                                    <div class="ml-3 flex-1">
                                                        <p class="text-sm font-medium text-gray-900 dark:text-white">Hội thảo công nghệ 2024</p>
                                                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">2 ngày trước</p>
                                                    </div>
                                                </div>
                                            </a>
                                        </div>
                                    </div>

                                    <div class="p-3 border-t border-gray-200 dark:border-gray-700">
                                        <button class="w-full text-center text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400 font-medium">
                                            Đánh dấu tất cả đã đọc
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Profile dropdown -->
                            <div class="relative" x-data="{ open: false }">
                                <button @click="open = !open" class="flex items-center p-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-full">
                                    <div class="w-8 h-8 bg-gradient-to-r from-primary-500 to-purple-500 rounded-full flex items-center justify-center">
                                        <i class="fas fa-user text-white text-sm"></i>
                                    </div>
                                </button>
                                
                                <div x-show="open" 
                                     @click.away="open = false"
                                     x-transition:enter="transition ease-out duration-100"
                                     x-transition:enter-start="transform opacity-0 scale-95"
                                     x-transition:enter-end="transform opacity-100 scale-100"
                                     x-transition:leave="transition ease-in duration-75"
                                     x-transition:leave-start="transform opacity-100 scale-100"
                                     x-transition:leave-end="transform opacity-0 scale-95"
                                     class="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white dark:bg-gray-800 py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                                    <a href="{{ route('profile.index') }}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">
                                        <i class="fas fa-user mr-2"></i>Hồ sơ
                                    </a>
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">
                                        <i class="fas fa-cog mr-2"></i>Cài đặt
                                    </a>
                                    <form method="POST" action="{{ route('logout') }}">
                                        @csrf
                                        <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">
                                            <i class="fas fa-sign-out-alt mr-2"></i>Đăng xuất
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page content -->
            <main class="flex-1 overflow-auto bg-morning-light dark:bg-gray-900">
                <div class="content-spacing max-w-7xl mx-auto w-full">
                    @if(session('success'))
                        <div class="mb-6 rounded-md bg-green-50 dark:bg-green-900/20 p-4 border border-green-200 dark:border-green-800">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-check-circle text-green-400"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-green-800 dark:text-green-200">
                                        {{ session('success') }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="mb-6 rounded-md bg-red-50 dark:bg-red-900/20 p-4 border border-red-200 dark:border-red-800">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-exclamation-circle text-red-400"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-red-800 dark:text-red-200">
                                        {{ session('error') }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    @endif

                    @yield('content')
                </div>
            </main>
        </div>
    </div>

    <!-- Floating Theme Toggle for Mobile -->
    <div class="lg:hidden theme-toggle-floating"
         :class="$store.theme.dark ? 'dark' : ''"
         @click="$store.theme.toggle()">
        <i class="fas fa-sun theme-icon"
           x-show="!$store.theme.dark"
           x-transition:enter="transition ease-out duration-300"
           x-transition:enter-start="opacity-0 rotate-180 scale-50"
           x-transition:enter-end="opacity-100 rotate-0 scale-100"></i>
        <i class="fas fa-moon theme-icon"
           x-show="$store.theme.dark"
           x-transition:enter="transition ease-out duration-300"
           x-transition:enter-start="opacity-0 rotate-180 scale-50"
           x-transition:enter-end="opacity-100 rotate-0 scale-100"></i>
    </div>

    <!-- Scripts -->
    <script src="{{ mix('js/app.js') }}"></script>
    @stack('scripts')
</body>
</html>
