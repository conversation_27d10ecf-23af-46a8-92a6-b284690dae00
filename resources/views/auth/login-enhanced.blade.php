<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="h-full">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Đăng nhập - {{ config('app.name', 'Student Portal') }}</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    
    <!-- Custom CSS -->
    <link href="{{ mix('css/app.css') }}" rel="stylesheet">
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <style>
        /* Additional inline styles for enhanced login */
        .login-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: relative;
            overflow: hidden;
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
            animation: backgroundShift 20s ease-in-out infinite;
        }

        @keyframes backgroundShift {
            0%, 100% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(1.1) rotate(1deg); }
        }

        .form-floating-label {
            position: relative;
        }

        .form-floating-label input:focus + label,
        .form-floating-label input:not(:placeholder-shown) + label {
            transform: translateY(-1.5rem) scale(0.85);
            color: #3b82f6;
        }

        .form-floating-label label {
            position: absolute;
            left: 1rem;
            top: 0.75rem;
            transition: all 0.3s ease;
            pointer-events: none;
            color: #6b7280;
        }

        .ripple-effect {
            position: relative;
            overflow: hidden;
        }

        .ripple-effect::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: translate(-50%, -50%);
            transition: width 0.6s, height 0.6s;
        }

        .ripple-effect:active::after {
            width: 300px;
            height: 300px;
        }

        /* Mobile optimizations */
        @media (max-width: 1023px) {
            .glass-effect {
                margin: 1rem;
                padding: 1.5rem;
            }
        }

        @media (max-width: 640px) {
            .glass-effect {
                margin: 0.5rem;
                padding: 1.25rem;
            }
        }
    </style>
</head>
<body class="h-full overflow-hidden" x-data="{ 
    showPassword: false, 
    isLoading: false,
    darkMode: localStorage.getItem('darkMode') === 'true' || false,
    toggleDarkMode() {
        this.darkMode = !this.darkMode;
        localStorage.setItem('darkMode', this.darkMode);
        document.documentElement.classList.toggle('dark', this.darkMode);
    },
    submitForm() {
        this.isLoading = true;
        setTimeout(() => {
            document.getElementById('loginForm').submit();
        }, 500);
    }
}" x-init="document.documentElement.classList.toggle('dark', darkMode)" :class="darkMode ? 'dark' : ''">

    <!-- Modern Background -->
    <div class="fixed inset-0 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-indigo-900">
        <!-- Geometric patterns -->
        <div class="absolute inset-0 overflow-hidden">
            <!-- Large circles -->
            <div class="absolute -top-40 -right-40 w-96 h-96 bg-gradient-to-br from-blue-400/10 to-indigo-500/10 rounded-full blur-3xl animate-float"></div>
            <div class="absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-to-br from-purple-400/10 to-pink-500/10 rounded-full blur-3xl animate-float" style="animation-delay: 3s;"></div>

            <!-- Medium circles -->
            <div class="absolute top-1/4 left-1/4 w-64 h-64 bg-gradient-to-br from-cyan-400/8 to-blue-500/8 rounded-full blur-2xl animate-float" style="animation-delay: 1s;"></div>
            <div class="absolute bottom-1/4 right-1/4 w-64 h-64 bg-gradient-to-br from-violet-400/8 to-purple-500/8 rounded-full blur-2xl animate-float" style="animation-delay: 4s;"></div>

            <!-- Grid pattern overlay -->
            <div class="absolute inset-0 opacity-[0.02] dark:opacity-[0.05]" style="background-image: radial-gradient(circle at 1px 1px, rgba(59,130,246,0.5) 1px, transparent 0); background-size: 40px 40px;"></div>
        </div>
    </div>

    <!-- Dark mode toggle -->
    <button @click="toggleDarkMode()" class="fixed top-6 right-6 z-50 p-3 glass-effect rounded-full hover:bg-white/20 dark:hover:bg-gray-700/50 transition-all duration-300 group">
        <i class="fas fa-sun text-yellow-500 dark:hidden group-hover:scale-110 transition-transform duration-300"></i>
        <i class="fas fa-moon text-blue-400 hidden dark:block group-hover:scale-110 transition-transform duration-300"></i>
    </button>

    <div class="min-h-screen flex relative z-10">
        <!-- Left side - Illustration/Branding -->
        <div class="hidden lg:flex lg:w-1/2 xl:w-3/5 lg:flex-col lg:justify-center lg:px-8 xl:px-12 relative">
            <div class="max-w-xl mx-auto">
                <!-- Hero Section -->
                <div class="text-center mb-12">
                    <!-- Logo with modern design -->
                    <div class="relative mb-8">
                        <div class="mx-auto h-28 w-28 bg-gradient-to-br from-blue-500 via-indigo-500 to-purple-600 rounded-3xl flex items-center justify-center shadow-2xl transform hover:scale-105 transition-all duration-500 relative overflow-hidden">
                            <!-- Shine effect -->
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 transform translate-x-[-100%] animate-pulse"></div>
                            <i class="fas fa-graduation-cap text-4xl text-white relative z-10"></i>
                        </div>
                        <!-- Floating elements around logo -->
                        <div class="absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full animate-bounce" style="animation-delay: 0.5s;"></div>
                        <div class="absolute -bottom-2 -left-2 w-4 h-4 bg-pink-400 rounded-full animate-bounce" style="animation-delay: 1s;"></div>
                    </div>

                    <!-- Welcome text with better typography -->
                    <div class="space-y-4">
                        <h1 class="text-4xl lg:text-5xl font-bold text-slate-800 dark:text-white font-poppins leading-tight">
                            Chào mừng đến với
                        </h1>
                        <h2 class="text-5xl lg:text-6xl font-bold bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent font-poppins leading-tight">
                            Student Portal
                        </h2>
                        <p class="text-xl text-slate-600 dark:text-slate-300 max-w-md mx-auto leading-relaxed">
                            Nền tảng học tập thông minh dành cho sinh viên hiện đại
                        </p>
                    </div>
                </div>

                <!-- Feature highlights -->
                <div class="grid grid-cols-2 gap-6 mb-8">
                    <div class="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 dark:border-slate-700/50 hover:scale-105 transition-all duration-300 group">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-chart-line text-xl text-white"></i>
                        </div>
                        <h3 class="font-semibold text-slate-800 dark:text-white mb-2">Theo dõi điểm số</h3>
                        <p class="text-sm text-slate-600 dark:text-slate-300">Xem điểm số và kết quả học tập</p>
                    </div>

                    <div class="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 dark:border-slate-700/50 hover:scale-105 transition-all duration-300 group">
                        <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-calendar-alt text-xl text-white"></i>
                        </div>
                        <h3 class="font-semibold text-slate-800 dark:text-white mb-2">Lịch học</h3>
                        <p class="text-sm text-slate-600 dark:text-slate-300">Quản lý thời khóa biểu cá nhân</p>
                    </div>

                    <div class="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 dark:border-slate-700/50 hover:scale-105 transition-all duration-300 group">
                        <div class="w-12 h-12 bg-gradient-to-br from-emerald-500 to-teal-500 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-wallet text-xl text-white"></i>
                        </div>
                        <h3 class="font-semibold text-slate-800 dark:text-white mb-2">Ví điện tử</h3>
                        <p class="text-sm text-slate-600 dark:text-slate-300">Thanh toán học phí tiện lợi</p>
                    </div>

                    <div class="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 dark:border-slate-700/50 hover:scale-105 transition-all duration-300 group">
                        <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-bell text-xl text-white"></i>
                        </div>
                        <h3 class="font-semibold text-slate-800 dark:text-white mb-2">Thông báo</h3>
                        <p class="text-sm text-slate-600 dark:text-slate-300">Cập nhật tin tức từ trường</p>
                    </div>
                </div>

                <!-- Stats or additional info -->
                <div class="text-center">
                    <div class="inline-flex items-center space-x-6 text-sm text-slate-600 dark:text-slate-400">
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                            <span>Hệ thống hoạt động 24/7</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                            <span>Bảo mật cao</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right side - Login Form -->
        <div class="flex-1 lg:w-1/2 xl:w-2/5 flex flex-col justify-center py-8 px-4 sm:px-6 lg:px-8 xl:px-12">
            <div class="mx-auto w-full max-w-md">
                <!-- Glass card container -->
                <div class="bg-white/80 dark:bg-slate-800/80 backdrop-blur-xl rounded-3xl p-8 shadow-2xl border border-white/20 dark:border-slate-700/50">
                    <!-- Mobile logo -->
                    <div class="text-center mb-8 lg:hidden">
                        <div class="mx-auto h-16 w-16 bg-gradient-to-br from-blue-500 via-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center mb-4 shadow-xl">
                            <i class="fas fa-graduation-cap text-2xl text-white"></i>
                        </div>
                        <h2 class="text-3xl font-bold text-slate-800 dark:text-white font-poppins">
                            Đăng nhập
                        </h2>
                        <p class="mt-2 text-slate-600 dark:text-slate-400">
                            Chào mừng bạn trở lại!
                        </p>
                    </div>

                    <!-- Desktop header -->
                    <div class="hidden lg:block text-center mb-8">
                        <div class="mb-6">
                            <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 via-indigo-500 to-purple-600 rounded-2xl shadow-lg">
                                <i class="fas fa-user-graduate text-2xl text-white"></i>
                            </div>
                        </div>
                        <h2 class="text-3xl font-bold text-slate-800 dark:text-white font-poppins mb-2">
                            Đăng nhập
                        </h2>
                        <p class="text-slate-600 dark:text-slate-400">
                            Vui lòng đăng nhập để truy cập hệ thống
                        </p>
                    </div>

                    <!-- Error Messages -->
                    @if ($errors->any())
                        <div class="mb-6 bg-red-50/80 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-2xl p-4 backdrop-blur-sm">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-exclamation-triangle text-red-500"></i>
                                </div>
                                <div class="ml-3">
                                    <ul class="text-sm text-red-700 dark:text-red-200 space-y-1">
                                        @foreach ($errors->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>
                        </div>
                    @endif

                    @if (session('error'))
                        <div class="mb-6 bg-red-50/80 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-2xl p-4 backdrop-blur-sm">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-exclamation-triangle text-red-500"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-red-700 dark:text-red-200">
                                        {{ session('error') }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Login Form -->
                    <form method="POST" action="{{ route('login') }}" class="space-y-6" id="loginForm">
                        @csrf

                        <!-- Login Field -->
                        <div class="space-y-2">
                            <label for="login" class="block text-sm font-semibold text-slate-700 dark:text-slate-300">
                                Email hoặc mã sinh viên
                            </label>
                            <div class="relative group">
                                <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                    <i class="fas fa-user text-slate-400 group-focus-within:text-blue-500 transition-colors duration-300"></i>
                                </div>
                                <input
                                    id="login"
                                    name="login"
                                    type="text"
                                    required
                                    autocomplete="username"
                                    autofocus
                                    value="{{ old('login') }}"
                                    class="w-full pl-12 pr-4 py-4 bg-white/90 dark:bg-slate-700/90 border border-slate-200 dark:border-slate-600 rounded-xl text-slate-900 dark:text-white placeholder-slate-500 dark:placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 shadow-sm hover:shadow-md @error('login') border-red-400 dark:border-red-500 @enderror"
                                    placeholder="Nhập email hoặc mã sinh viên"
                                >
                            </div>
                            @error('login')
                                <p class="mt-2 text-sm text-red-600 dark:text-red-400 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>{{ $message }}
                                </p>
                            @enderror
                        </div>

                        <!-- Password Field -->
                        <div class="space-y-2">
                            <label for="password" class="block text-sm font-semibold text-slate-700 dark:text-slate-300">
                                Mật khẩu
                            </label>
                            <div class="relative group">
                                <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                    <i class="fas fa-lock text-slate-400 group-focus-within:text-blue-500 transition-colors duration-300"></i>
                                </div>
                                <input
                                    id="password"
                                    name="password"
                                    :type="showPassword ? 'text' : 'password'"
                                    required
                                    autocomplete="current-password"
                                    class="w-full pl-12 pr-12 py-4 bg-white/90 dark:bg-slate-700/90 border border-slate-200 dark:border-slate-600 rounded-xl text-slate-900 dark:text-white placeholder-slate-500 dark:placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 shadow-sm hover:shadow-md @error('password') border-red-400 dark:border-red-500 @enderror"
                                    placeholder="Nhập mật khẩu"
                                >
                                <button
                                    type="button"
                                    @click="showPassword = !showPassword"
                                    class="absolute inset-y-0 right-0 pr-4 flex items-center hover:scale-110 transition-transform duration-300"
                                >
                                    <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'" class="text-slate-400 hover:text-slate-600 dark:hover:text-slate-300"></i>
                                </button>
                            </div>
                            @error('password')
                                <p class="mt-2 text-sm text-red-600 dark:text-red-400 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>{{ $message }}
                                </p>
                            @enderror
                        </div>

                        <!-- Remember Me & Forgot Password -->
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <input
                                    id="remember"
                                    name="remember"
                                    type="checkbox"
                                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded transition-colors duration-300"
                                    {{ old('remember') ? 'checked' : '' }}
                                >
                                <label for="remember" class="ml-2 block text-sm text-slate-700 dark:text-slate-300">
                                    Ghi nhớ đăng nhập
                                </label>
                            </div>

                            @if (Route::has('password.request'))
                                <div class="text-sm">
                                    <a href="{{ route('password.request') }}" class="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 transition-colors duration-300">
                                        Quên mật khẩu?
                                    </a>
                                </div>
                            @endif
                        </div>

                        <!-- Submit Button -->
                        <div class="pt-2">
                            <button
                                type="button"
                                @click="submitForm()"
                                :disabled="isLoading"
                                class="group relative w-full flex justify-center py-4 px-6 border border-transparent text-base font-semibold rounded-xl text-white bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 hover:from-blue-700 hover:via-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-300 transform hover:scale-[1.02] hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed ripple-effect"
                            >
                                <span x-show="!isLoading" class="flex items-center">
                                    <i class="fas fa-sign-in-alt mr-3"></i>
                                    Đăng nhập
                                </span>
                                <span x-show="isLoading" class="flex items-center">
                                    <i class="fas fa-spinner fa-spin mr-3"></i>
                                    Đang đăng nhập...
                                </span>
                            </button>
                        </div>

                        <!-- Divider -->
                        <div class="mt-8">
                            <div class="relative">
                                <div class="absolute inset-0 flex items-center">
                                    <div class="w-full border-t border-slate-200 dark:border-slate-600"></div>
                                </div>
                                <div class="relative flex justify-center text-sm">
                                    <span class="px-4 bg-white/90 dark:bg-slate-800/90 text-slate-500 dark:text-slate-400 rounded-full font-medium">Hoặc</span>
                                </div>
                            </div>
                        </div>

                        <!-- Google Login -->
                        <div class="mt-6">
                            <a
                                href="{{ route('auth.google') }}"
                                class="w-full inline-flex justify-center items-center py-3 px-4 border border-slate-200 dark:border-slate-600 rounded-xl shadow-sm bg-white/90 dark:bg-slate-700/90 text-sm font-medium text-slate-700 dark:text-slate-300 hover:bg-white dark:hover:bg-slate-600 transition-all duration-300 transform hover:scale-[1.02] hover:shadow-md"
                            >
                                <svg class="w-5 h-5 mr-3" viewBox="0 0 24 24">
                                    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                                    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                                    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                                    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                                </svg>
                                Đăng nhập với Google
                            </a>
                        </div>
                    </form>

                    <!-- Test Accounts Info -->
                    <div class="mt-8">
                        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800 rounded-2xl p-4 backdrop-blur-sm">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-info text-white text-sm"></i>
                                    </div>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-semibold text-blue-800 dark:text-blue-200 mb-2">
                                        Tài khoản demo
                                    </h3>
                                    <div class="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                                        <div class="flex items-center">
                                            <i class="fas fa-envelope w-4 mr-2"></i>
                                            <span class="font-mono"><EMAIL></span>
                                        </div>
                                        <div class="flex items-center">
                                            <i class="fas fa-id-card w-4 mr-2"></i>
                                            <span class="font-mono">2025300232</span>
                                        </div>
                                        <div class="flex items-center">
                                            <i class="fas fa-key w-4 mr-2"></i>
                                            <span class="font-mono">123@123</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="{{ mix('js/app.js') }}"></script>
</body>
</html>
