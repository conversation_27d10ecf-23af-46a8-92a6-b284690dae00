@extends('layouts.modern')

@section('title', 'Dashboard')
@section('page-title', 'Dashboard')

@section('content')
<!-- Welcome Section -->
<div class="mb-8">
    <div class="bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 rounded-2xl shadow-xl overflow-hidden">
        <div class="px-8 py-12 text-white relative">
            <div class="absolute inset-0 bg-black bg-opacity-10"></div>
            <div class="relative">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <h1 class="text-4xl font-bold mb-4">
                            Chào mừng, {{ $student->display_name }}! 👋
                        </h1>
                        <div class="space-y-2 text-lg">
                            <p class="flex items-center text-white/90">
                                <i class="fas fa-id-card mr-3 w-5"></i>
                                Mã sinh viên: <span class="font-semibold ml-2">{{ $student->user_code }}</span>
                            </p>
                            <p class="flex items-center text-white/90">
                                <i class="fas fa-envelope mr-3 w-5"></i>
                                Email: <span class="font-semibold ml-2">{{ $student->user_email }}</span>
                            </p>
                        </div>
                    </div>
                    <div class="hidden lg:block">
                        <div class="w-32 h-32 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm border border-white/20">
                            <i class="fas fa-graduation-cap text-6xl text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total Classes -->
    <div class="card-glass rounded-xl p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-14 h-14 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                    <i class="fas fa-chalkboard-teacher text-white text-xl"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                    Tổng số lớp
                </p>
                <p class="text-3xl font-bold text-gray-900 dark:text-white" data-classes-count>
                    {{ $stats['total_classes'] }}
                </p>
            </div>
        </div>
    </div>

    <!-- Active Classes -->
    <div class="card-glass rounded-xl p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-14 h-14 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
                    <i class="fas fa-play-circle text-white text-xl"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                    Lớp đang học
                </p>
                <p class="text-3xl font-bold text-gray-900 dark:text-white">
                    {{ $stats['active_classes'] }}
                </p>
            </div>
        </div>
    </div>

    <!-- Total Credits -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-14 h-14 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                    <i class="fas fa-certificate text-white text-xl"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                    Tổng tín chỉ
                </p>
                <p class="text-3xl font-bold text-gray-900 dark:text-white">
                    {{ $stats['total_credits'] }}
                </p>
            </div>
        </div>
    </div>

    <!-- Wallet Balance -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-14 h-14 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center shadow-lg">
                    <i class="fas fa-wallet text-white text-xl"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                    Số dư ví
                </p>
                <p class="text-3xl font-bold text-gray-900 dark:text-white" data-wallet-balance>
                    {{ number_format($stats['wallet_balance']) }} VNĐ
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Latest Announcements -->
<div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 mb-8">
    <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-xl font-bold text-gray-900 dark:text-white flex items-center">
            <div class="w-8 h-8 bg-gradient-to-r from-red-500 to-pink-600 rounded-lg flex items-center justify-center mr-3">
                <i class="fas fa-bullhorn text-white text-sm"></i>
            </div>
            Thông báo mới nhất
        </h3>
        <a href="{{ route('announcements.index') }}" class="inline-flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-lg transition-colors">
            Xem tất cả
        </a>
    </div>
    <div class="p-6">
        <div class="space-y-4">
            <div class="flex items-start p-4 bg-red-50 dark:bg-red-900/20 rounded-xl border border-red-200 dark:border-red-800">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400"></i>
                    </div>
                </div>
                <div class="ml-4 flex-1">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="font-bold text-gray-900 dark:text-white">Thay đổi lịch thi cuối kỳ</p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Sinh viên vui lòng kiểm tra lịch thi mới được cập nhật</p>
                            <p class="text-xs text-gray-500 dark:text-gray-500 mt-1">2 giờ trước</p>
                        </div>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                            Khẩn cấp
                        </span>
                    </div>
                </div>
            </div>

            <div class="flex items-start p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-xl border border-yellow-200 dark:border-yellow-800">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-yellow-100 dark:bg-yellow-900 rounded-full flex items-center justify-center">
                        <i class="fas fa-dollar-sign text-yellow-600 dark:text-yellow-400"></i>
                    </div>
                </div>
                <div class="ml-4 flex-1">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="font-bold text-gray-900 dark:text-white">Thông báo đóng học phí</p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Hạn cuối đóng học phí học kỳ 2: 15/01/2024</p>
                            <p class="text-xs text-gray-500 dark:text-gray-500 mt-1">1 ngày trước</p>
                        </div>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                            Mới
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Content Grid -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- Today's Schedule -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-xl font-bold text-gray-900 dark:text-white flex items-center">
                <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-calendar-day text-white text-sm"></i>
                </div>
                Lịch học hôm nay
            </h3>
            <a href="{{ route('schedule.index') }}" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors">
                Xem tất cả
            </a>
        </div>
        <div class="p-6">
            @if($todaySchedule->count() > 0)
                <div class="space-y-4">
                    @foreach($todaySchedule as $schedule)
                        <div class="flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-xl border border-gray-100 dark:border-gray-600">
                            <div class="flex-shrink-0">
                                <div class="w-12 h-12 bg-gradient-to-r from-blue-100 to-blue-200 dark:from-blue-900 dark:to-blue-800 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-clock text-blue-600 dark:text-blue-400"></i>
                                </div>
                            </div>
                            <div class="ml-4 flex-1">
                                <p class="text-sm text-gray-500 dark:text-gray-400 font-medium">
                                    {{ $schedule->time ?? 'Chưa xác định' }}
                                </p>
                                <p class="font-bold text-gray-900 dark:text-white text-lg">
                                    {{ $schedule->subject ?? 'Môn học' }}
                                </p>
                                <p class="text-sm text-gray-600 dark:text-gray-300">
                                    {{ $schedule->room ?? 'Phòng học' }}
                                </p>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-16">
                    <div class="w-20 h-20 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-calendar-times text-3xl text-gray-400"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Không có lịch học</h4>
                    <p class="text-gray-500 dark:text-gray-400">Hôm nay bạn không có lịch học nào</p>
                </div>
            @endif
        </div>
    </div>

    <!-- Recent Grades -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-xl font-bold text-gray-900 dark:text-white flex items-center">
                <div class="w-8 h-8 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-chart-line text-white text-sm"></i>
                </div>
                Điểm số gần đây
            </h3>
            <a href="{{ route('grades.index') }}" class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors">
                Xem tất cả
            </a>
        </div>
        <div class="p-6">
            @if($recentGrades->count() > 0)
                <div class="space-y-4">
                    @foreach($recentGrades as $grade)
                        <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-xl border border-gray-100 dark:border-gray-600">
                            <div class="flex-1">
                                <p class="font-bold text-gray-900 dark:text-white text-lg">
                                    {{ $grade->subject ?? 'Môn học' }}
                                </p>
                                <p class="text-sm text-gray-500 dark:text-gray-400 font-medium">
                                    {{ $grade->type ?? 'Loại điểm' }}
                                </p>
                            </div>
                            <div class="text-right">
                                <span class="text-3xl font-bold text-green-600 dark:text-green-400">
                                    {{ $grade->score ?? '0' }}
                                </span>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-16">
                    <div class="w-20 h-20 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-chart-line text-3xl text-gray-400"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Chưa có điểm số</h4>
                    <p class="text-gray-500 dark:text-gray-400">Điểm số sẽ được cập nhật sau khi có kết quả</p>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-xl font-bold text-gray-900 dark:text-white flex items-center">
            <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                <i class="fas fa-bolt text-white text-sm"></i>
            </div>
            Thao tác nhanh
        </h3>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-2 lg:grid-cols-4 gap-6">
            <a href="{{ route('classes.index') }}"
               class="group flex flex-col items-center p-6 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-xl border-2 border-blue-200 dark:border-blue-700 hover:border-blue-300 dark:hover:border-blue-600 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform shadow-lg">
                    <i class="fas fa-chalkboard-teacher text-white text-2xl"></i>
                </div>
                <span class="text-sm font-bold text-blue-700 dark:text-blue-300 text-center">
                    Lớp học của tôi
                </span>
            </a>

            <a href="{{ route('schedule.index') }}"
               class="group flex flex-col items-center p-6 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-xl border-2 border-green-200 dark:border-green-700 hover:border-green-300 dark:hover:border-green-600 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform shadow-lg">
                    <i class="fas fa-calendar-alt text-white text-2xl"></i>
                </div>
                <span class="text-sm font-bold text-green-700 dark:text-green-300 text-center">
                    Lịch học
                </span>
            </a>

            <a href="{{ route('grades.index') }}"
               class="group flex flex-col items-center p-6 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-xl border-2 border-purple-200 dark:border-purple-700 hover:border-purple-300 dark:hover:border-purple-600 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform shadow-lg">
                    <i class="fas fa-chart-line text-white text-2xl"></i>
                </div>
                <span class="text-sm font-bold text-purple-700 dark:text-purple-300 text-center">
                    Điểm số
                </span>
            </a>

            <a href="{{ route('wallet.index') }}"
               class="group flex flex-col items-center p-6 bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 rounded-xl border-2 border-orange-200 dark:border-orange-700 hover:border-orange-300 dark:hover:border-orange-600 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <div class="w-16 h-16 bg-gradient-to-r from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform shadow-lg">
                    <i class="fas fa-wallet text-white text-2xl"></i>
                </div>
                <span class="text-sm font-bold text-orange-700 dark:text-orange-300 text-center">
                    Ví điện tử
                </span>
            </a>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animate elements on load with stagger effect
    const animateElements = () => {
        // Welcome section
        const welcomeSection = document.querySelector('.bg-gradient-to-r');
        if (welcomeSection) {
            welcomeSection.style.opacity = '0';
            welcomeSection.style.transform = 'translateY(-20px)';
            setTimeout(() => {
                welcomeSection.style.transition = 'all 0.8s ease-out';
                welcomeSection.style.opacity = '1';
                welcomeSection.style.transform = 'translateY(0)';
            }, 100);
        }

        // Stats cards
        const statsCards = document.querySelectorAll('.grid > div');
        statsCards.forEach((card, index) => {
            if (index < 4) { // Only first 4 cards (stats)
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px) scale(0.95)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease-out';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0) scale(1)';
                }, 300 + (index * 150));
            }
        });

        // Content sections
        const contentSections = document.querySelectorAll('.bg-white.dark\\:bg-gray-800');
        contentSections.forEach((section, index) => {
            if (index >= 4) { // Skip stats cards
                section.style.opacity = '0';
                section.style.transform = 'translateY(40px)';
                setTimeout(() => {
                    section.style.transition = 'all 0.7s ease-out';
                    section.style.opacity = '1';
                    section.style.transform = 'translateY(0)';
                }, 900 + ((index - 4) * 200));
            }
        });
    };

    // Start animations
    animateElements();

    // Add hover effects to quick actions
    const quickActions = document.querySelectorAll('a[href*="route"]');
    quickActions.forEach(action => {
        action.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });

        action.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // Counter animation for stats
    const animateCounters = () => {
        const counters = document.querySelectorAll('[data-classes-count], [data-wallet-balance]');
        counters.forEach(counter => {
            const target = parseInt(counter.textContent.replace(/[^\d]/g, '')) || 0;
            let current = 0;
            const increment = target / 30;
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                if (counter.hasAttribute('data-wallet-balance')) {
                    counter.textContent = Math.round(current).toLocaleString() + ' VNĐ';
                } else {
                    counter.textContent = Math.round(current);
                }
            }, 50);
        });
    };

    // Start counter animation after cards are visible
    setTimeout(animateCounters, 1200);
});
</script>
@endpush
