<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateStudentClassesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('student_classes', function (Blueprint $table) {
            $table->id();
            $table->string('class_code', 20)->unique(); // Mã lớp học (VD: IT101_01)
            $table->string('class_name'); // Tên lớp học
            $table->unsignedBigInteger('subject_id'); // ID môn học
            $table->unsignedBigInteger('teacher_id')->nullable(); // ID giảng viên
            $table->string('semester', 20); // Học kỳ (VD: 2024-1, 2024-2)
            $table->integer('academic_year'); // Năm học (VD: 2024)
            $table->string('room')->nullable(); // Phòng học
            $table->integer('max_students')->default(40); // Số sinh viên tối đa
            $table->integer('current_students')->default(0); // Số sinh viên hiện tại
            $table->date('start_date'); // Ngày bắt đầu
            $table->date('end_date'); // Ngày kết thúc
            $table->enum('status', ['upcoming', 'ongoing', 'completed', 'cancelled'])->default('upcoming');
            $table->json('schedule')->nullable(); // Lịch học (JSON: [{day: 2, start: '07:00', end: '09:00'}])
            $table->text('description')->nullable(); // Mô tả lớp học
            $table->timestamps();

            $table->foreign('subject_id')->references('id')->on('subjects')->onDelete('cascade');
            $table->foreign('teacher_id')->references('id')->on('user')->onDelete('set null');
            $table->index(['semester', 'academic_year', 'status']);
            $table->index(['teacher_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('student_classes');
    }
}
