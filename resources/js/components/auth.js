/**
 * Authentication Components JavaScript
 * Handles login, password reset, and other auth-related functionality
 */

// Password Toggle Functionality
function togglePassword(inputId = 'password') {
    const passwordInput = document.getElementById(inputId);
    const toggleIcon = document.getElementById('togglePasswordIcon');

    if (passwordInput && toggleIcon) {
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.classList.remove('fa-eye');
            toggleIcon.classList.add('fa-eye-slash');
        } else {
            passwordInput.type = 'password';
            toggleIcon.classList.remove('fa-eye-slash');
            toggleIcon.classList.add('fa-eye');
        }
    }
}

// Password Strength Checker
function checkPasswordStrength(password) {
    let strength = 0;
    let feedback = [];
    
    if (password.length >= 8) strength += 1;
    else feedback.push('<PERSON><PERSON><PERSON> thiểu 8 ký tự');
    
    if (/[a-z]/.test(password)) strength += 1;
    else feedback.push('Chữ thường');
    
    if (/[A-Z]/.test(password)) strength += 1;
    else feedback.push('Chữ hoa');
    
    if (/[0-9]/.test(password)) strength += 1;
    else feedback.push('Số');
    
    if (/[^A-Za-z0-9]/.test(password)) strength += 1;
    else feedback.push('Ký tự đặc biệt');
    
    return { strength, feedback };
}

// Password Match Checker
function checkPasswordMatch(passwordId = 'password', confirmId = 'password-confirm', matchTextId = 'passwordMatchText') {
    const passwordInput = document.getElementById(passwordId);
    const passwordConfirmInput = document.getElementById(confirmId);
    const matchText = document.getElementById(matchTextId);
    
    if (!passwordInput || !passwordConfirmInput || !matchText) return;
    
    const password = passwordInput.value;
    const confirmPassword = passwordConfirmInput.value;
    
    if (confirmPassword.length > 0) {
        if (password === confirmPassword) {
            matchText.innerHTML = '<i class="fas fa-check text-success me-1"></i>Mật khẩu khớp';
            passwordConfirmInput.classList.add('is-valid');
            passwordConfirmInput.classList.remove('is-invalid');
        } else {
            matchText.innerHTML = '<i class="fas fa-times text-danger me-1"></i>Mật khẩu không khớp';
            passwordConfirmInput.classList.add('is-invalid');
            passwordConfirmInput.classList.remove('is-valid');
        }
    } else {
        matchText.textContent = '';
        passwordConfirmInput.classList.remove('is-valid', 'is-invalid');
    }
}

// Email Validation
function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Form Enhancement
function enhanceAuthForms() {
    // Login Form Enhancement
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        const loginInput = loginForm.querySelector('#login');
        const passwordInput = loginForm.querySelector('#password');
        const loginBtn = loginForm.querySelector('#loginBtn');

        // Add focus effects
        [loginInput, passwordInput].forEach(input => {
            if (input) {
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'scale(1.02)';
                    this.parentElement.style.transition = 'transform 0.2s ease';
                });
                
                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'scale(1)';
                });
            }
        });
        
        // Form submission
        loginForm.addEventListener('submit', function(e) {
            if (loginBtn) {
                loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Đang đăng nhập...';
                loginBtn.disabled = true;
            }
        });
    }
    
    // Password Reset Form Enhancement
    const resetForm = document.getElementById('resetForm');
    if (resetForm) {
        const emailInput = resetForm.querySelector('#email');
        const resetBtn = resetForm.querySelector('#resetBtn');
        
        if (emailInput) {
            // Add focus effects
            emailInput.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
                this.parentElement.style.transition = 'transform 0.2s ease';
            });
            
            emailInput.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
            
            // Email validation
            emailInput.addEventListener('input', function() {
                const email = this.value;
                
                if (validateEmail(email)) {
                    this.classList.add('is-valid');
                    this.classList.remove('is-invalid');
                    if (resetBtn) resetBtn.disabled = false;
                } else if (email.length > 0) {
                    this.classList.add('is-invalid');
                    this.classList.remove('is-valid');
                    if (resetBtn) resetBtn.disabled = true;
                } else {
                    this.classList.remove('is-valid', 'is-invalid');
                    if (resetBtn) resetBtn.disabled = false;
                }
            });
        }
        
        // Form submission
        resetForm.addEventListener('submit', function(e) {
            if (resetBtn) {
                resetBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Đang gửi...';
                resetBtn.disabled = true;
            }
        });
    }
    
    // Password Reset Form Enhancement
    const resetPasswordForm = document.getElementById('resetPasswordForm');
    if (resetPasswordForm) {
        const passwordInput = resetPasswordForm.querySelector('#password');
        const passwordConfirmInput = resetPasswordForm.querySelector('#password-confirm');
        const progressBar = resetPasswordForm.querySelector('.progress-bar');
        const strengthText = resetPasswordForm.querySelector('#passwordStrengthText');
        
        if (passwordInput && progressBar && strengthText) {
            // Password strength indicator
            passwordInput.addEventListener('input', function() {
                const password = this.value;
                const result = checkPasswordStrength(password);
                const percentage = (result.strength / 5) * 100;
                
                progressBar.style.width = percentage + '%';
                
                if (percentage < 40) {
                    progressBar.style.background = 'linear-gradient(90deg, #ff6b6b, #ff8e8e)';
                    strengthText.textContent = 'Yếu - ' + result.feedback.join(', ');
                } else if (percentage < 80) {
                    progressBar.style.background = 'linear-gradient(90deg, #ffd93d, #ffed4e)';
                    strengthText.textContent = 'Trung bình';
                } else {
                    progressBar.style.background = 'linear-gradient(90deg, #6bcf7f, #4facfe)';
                    strengthText.textContent = 'Mạnh';
                }
                
                checkPasswordMatch();
            });
        }
        
        if (passwordConfirmInput) {
            passwordConfirmInput.addEventListener('input', function() {
                checkPasswordMatch();
            });
        }
        
        // Add focus effects to all inputs
        const inputs = resetPasswordForm.querySelectorAll('input');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
                this.parentElement.style.transition = 'transform 0.2s ease';
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });
        
        // Form submission
        resetPasswordForm.addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Đang đặt lại...';
                submitBtn.disabled = true;
            }
        });
    }
}

// Theme Toggle Functionality
function initThemeToggle() {
    const themeToggle = document.getElementById('themeToggle');
    const body = document.body;
    
    if (!themeToggle) return;
    
    // Get saved theme or default to dark
    const savedTheme = localStorage.getItem('theme') || 'dark';
    setTheme(savedTheme);
    updateToggleButton(savedTheme);
    
    // Theme toggle click handler
    themeToggle.addEventListener('click', function() {
        const currentTheme = body.getAttribute('data-theme') || 'dark';
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        
        setTheme(newTheme);
        updateToggleButton(newTheme);
        localStorage.setItem('theme', newTheme);
    });
    
    function setTheme(theme) {
        body.setAttribute('data-theme', theme);
    }
    
    function updateToggleButton(theme) {
        const icon = themeToggle.querySelector('i');
        if (icon) {
            if (theme === 'dark') {
                icon.className = 'fas fa-sun';
            } else {
                icon.className = 'fas fa-moon';
            }
        }
    }
    
    // Listen for system theme changes
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
        if (localStorage.getItem('theme') === 'auto') {
            setTheme(e.matches ? 'dark' : 'light');
            updateToggleButton(e.matches ? 'dark' : 'light');
        }
    });
}

// Background Effects
function initBackgroundEffects() {
    // Prevent auto scroll by ensuring body doesn't overflow
    document.body.style.overflowX = 'hidden';
    document.documentElement.style.overflowX = 'hidden';

    // Create floating particles (reduced for performance)
    function createParticle() {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.left = Math.random() * 100 + 'vw';
        particle.style.animationDuration = (Math.random() * 3 + 2) + 's';
        particle.style.opacity = Math.random() * 0.3 + 0.1;
        particle.style.pointerEvents = 'none';

        document.body.appendChild(particle);

        setTimeout(() => {
            if (particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
        }, 5000);
    }

    // Create tech elements (simplified)
    function createTechElements() {
        const techContainer = document.querySelector('.tech-elements');
        if (!techContainer) return;

        const techSymbols = ['{ }', '[ ]', '01', 'AI'];

        techSymbols.forEach((symbol, index) => {
            const element = document.createElement('div');
            element.className = 'tech-element';
            element.textContent = symbol;
            element.style.top = Math.random() * 60 + 20 + '%';
            element.style.left = Math.random() * 60 + 20 + '%';
            element.style.animationDelay = (index * 3) + 's';
            element.style.pointerEvents = 'none';
            techContainer.appendChild(element);
        });
    }

    // Create academic elements (simplified)
    function createAcademicElements() {
        const floatingContainer = document.querySelector('.floating-elements');
        if (!floatingContainer) return;

        const academicIcons = [
            'fas fa-graduation-cap',
            'fas fa-book',
            'fas fa-calculator',
            'fas fa-atom'
        ];

        academicIcons.forEach((iconClass, index) => {
            const element = document.createElement('div');
            element.className = 'floating-element';
            element.innerHTML = `<i class="${iconClass}"></i>`;
            element.style.top = Math.random() * 60 + 20 + '%';
            element.style.left = Math.random() * 60 + 20 + '%';
            element.style.animationDelay = (index * 4) + 's';
            element.style.pointerEvents = 'none';
            floatingContainer.appendChild(element);
        });
    }

    // Initialize effects with delay to prevent layout issues
    setTimeout(() => {
        createTechElements();
        createAcademicElements();

        // Create fewer particles
        for (let i = 0; i < 5; i++) {
            setTimeout(createParticle, i * 2000);
        }

        // Continue creating particles less frequently
        setInterval(createParticle, 5000);
    }, 1000);
}

// Initialize all auth components when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    enhanceAuthForms();
    initThemeToggle();
    initBackgroundEffects();
});

// Export functions for global use
window.togglePassword = togglePassword;
window.checkPasswordStrength = checkPasswordStrength;
window.checkPasswordMatch = checkPasswordMatch;
window.validateEmail = validateEmail;
