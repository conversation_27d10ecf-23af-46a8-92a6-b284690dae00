<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateClassEnrollmentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('class_enrollments', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('student_id'); // ID sinh viên
            $table->unsignedBigInteger('student_class_id'); // ID lớp học
            $table->date('enrollment_date'); // Ngày đăng ký
            $table->enum('status', ['enrolled', 'dropped', 'completed', 'failed'])->default('enrolled');
            $table->decimal('midterm_score', 4, 2)->nullable(); // Điểm giữa kỳ
            $table->decimal('final_score', 4, 2)->nullable(); // Điểm cuối kỳ
            $table->decimal('total_score', 4, 2)->nullable(); // Điểm tổng kết
            $table->string('grade', 2)->nullable(); // Xếp loại (A, B, C, D, F)
            $table->boolean('is_retake')->default(false); // Có phải học lại không
            $table->text('notes')->nullable(); // Ghi chú
            $table->timestamps();

            $table->foreign('student_id')->references('id')->on('user')->onDelete('cascade');
            $table->foreign('student_class_id')->references('id')->on('student_classes')->onDelete('cascade');
            $table->unique(['student_id', 'student_class_id']); // Một sinh viên chỉ đăng ký một lớp một lần
            $table->index(['student_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('class_enrollments');
    }
}
