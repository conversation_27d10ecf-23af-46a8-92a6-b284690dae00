@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* Import custom components */
@import './components/login-enhanced.css';

/* Custom base styles */
@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    @apply font-sans antialiased;
    @apply bg-gray-50 dark:bg-gray-900;
    @apply text-gray-900 dark:text-gray-100;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    @apply w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-gray-600 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400 dark:bg-gray-500;
  }
}

/* Custom component styles */
@layer components {
  /* Glass morphism effect */
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .glass-dark {
    @apply bg-gray-900/10 backdrop-blur-md border border-gray-700/20;
  }

  /* Gradient backgrounds */
  .gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .gradient-success {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }

  .gradient-warning {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  }

  .gradient-danger {
    background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
  }

  /* Card styles */
  .card {
    @apply bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700;
  }

  .card-hover {
    @apply transition-all duration-300 hover:shadow-soft-lg hover:-translate-y-1;
  }

  /* Button styles */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }

  .btn-secondary {
    @apply bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
  }

  .btn-success {
    @apply bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }

  .btn-warning {
    @apply bg-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500;
  }

  .btn-danger {
    @apply bg-danger-600 text-white hover:bg-danger-700 focus:ring-danger-500;
  }

  /* Input styles */
  .input {
    @apply block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white;
  }

  /* Navigation styles */
  .nav-link {
    @apply flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200;
  }

  .nav-link-active {
    @apply bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300;
  }

  .nav-link-inactive {
    @apply text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white;
  }
}

/* Custom utilities */
@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-purple-600 bg-clip-text text-transparent;
  }

  .border-gradient {
    border-image: linear-gradient(135deg, #667eea 0%, #764ba2 100%) 1;
  }
}

/* Layout fixes for sidebar */
.sidebar-scroll {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.sidebar-scroll::-webkit-scrollbar {
  width: 6px;
}

.sidebar-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-scroll::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.sidebar-scroll::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* Ensure proper flex layout */
.layout-container {
  display: flex;
  min-height: 100vh;
}

.layout-sidebar {
  flex-shrink: 0;
  width: 18rem; /* 288px */
}

.layout-main {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
}

/* Mobile responsive fixes */
@media (max-width: 1023px) {
  .layout-container {
    display: block;
  }

  .layout-sidebar {
    width: 18rem;
  }

  .layout-main {
    width: 100%;
  }
}

/* Smooth transitions */
.sidebar-transition {
  transition: transform 0.3s ease-in-out;
}

/* Content spacing */
.content-spacing {
  padding: 1.5rem;
}

@media (min-width: 640px) {
  .content-spacing {
    padding: 2rem;
  }
}

@media (min-width: 1024px) {
  .content-spacing {
    padding: 2.5rem;
  }
}

/* Student-friendly backgrounds inspired by modern education platforms */
.bg-student-light {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%);
}

.bg-student-warm {
  background: linear-gradient(135deg, #fefcfb 0%, #fef3c7 20%, #fde68a 100%);
}

.bg-student-cool {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 50%, #bae6fd 100%);
}

.bg-student-nature {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 50%, #bbf7d0 100%);
}

.bg-student-modern {
  background:
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(168, 85, 247, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(34, 197, 94, 0.08) 0%, transparent 50%),
    linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

/* Canvas LMS inspired */
.bg-canvas {
  background: #ffffff;
}

/* Google Classroom inspired */
.bg-classroom {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

/* Microsoft Teams Education inspired */
.bg-teams {
  background: linear-gradient(135deg, #f3f2f1 0%, #faf9f8 50%, #ffffff 100%);
}

/* Friendly gradient with subtle patterns */
.bg-education-modern {
  background:
    linear-gradient(135deg, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
    linear-gradient(45deg, rgba(168, 85, 247, 0.03) 0%, transparent 50%),
    #ffffff;
}

/* Soft morning light effect */
.bg-morning-light {
  background:
    radial-gradient(ellipse at top, rgba(255, 237, 213, 0.4) 0%, transparent 70%),
    radial-gradient(ellipse at bottom right, rgba(219, 234, 254, 0.4) 0%, transparent 70%),
    #ffffff;
}

/* Dark mode backgrounds */
.dark .bg-morning-light {
  background:
    radial-gradient(ellipse at top, rgba(59, 130, 246, 0.1) 0%, transparent 70%),
    radial-gradient(ellipse at bottom right, rgba(168, 85, 247, 0.1) 0%, transparent 70%),
    #111827;
}

.dark .bg-education-modern {
  background:
    linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
    linear-gradient(45deg, rgba(168, 85, 247, 0.05) 0%, transparent 50%),
    #1f2937;
}

/* Card improvements for better visibility */
.card-modern {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.05),
    0 2px 4px -1px rgba(0, 0, 0, 0.03);
}

.card-glass {
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.08),
    0 4px 16px rgba(0, 0, 0, 0.04);
}

.dark .card-glass {
  background: rgba(31, 41, 55, 0.85);
  border: 1px solid rgba(75, 85, 99, 0.3);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 4px 16px rgba(0, 0, 0, 0.2);
}

/* Friendly button styles */
.btn-friendly {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.25);
  transition: all 0.3s ease;
}

.btn-friendly:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px 0 rgba(59, 130, 246, 0.35);
}

/* Theme Toggle Styles */
.theme-toggle {
  position: relative;
  width: 60px;
  height: 32px;
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 4px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.theme-toggle.dark {
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  border-color: rgba(255, 255, 255, 0.1);
}

.theme-toggle-slider {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 24px;
  height: 24px;
  background: white;
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 2px 4px rgba(0, 0, 0, 0.2),
    0 1px 2px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.theme-toggle.dark .theme-toggle-slider {
  transform: translateX(28px);
  background: #1e293b;
  color: #fbbf24;
}

.theme-toggle:not(.dark) .theme-toggle-slider {
  color: #f59e0b;
}

/* Theme toggle icon animations */
.theme-icon {
  transition: all 0.3s ease;
  transform: scale(1);
}

.theme-toggle:hover .theme-icon {
  transform: scale(1.1);
}

.theme-toggle.dark .sun-icon {
  opacity: 0;
  transform: rotate(180deg) scale(0.5);
}

.theme-toggle:not(.dark) .moon-icon {
  opacity: 0;
  transform: rotate(-180deg) scale(0.5);
}

/* Floating theme toggle for better UX */
.theme-toggle-floating {
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 50;
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow:
    0 8px 25px rgba(59, 130, 246, 0.3),
    0 4px 10px rgba(0, 0, 0, 0.1);
  border: 3px solid rgba(255, 255, 255, 0.2);
}

.theme-toggle-floating:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow:
    0 12px 35px rgba(59, 130, 246, 0.4),
    0 6px 15px rgba(0, 0, 0, 0.15);
}

.theme-toggle-floating.dark {
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  box-shadow:
    0 8px 25px rgba(30, 41, 59, 0.3),
    0 4px 10px rgba(0, 0, 0, 0.2);
}

.theme-toggle-floating .theme-icon {
  font-size: 24px;
  color: white;
}