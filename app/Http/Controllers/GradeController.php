<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class GradeController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index()
    {
        $student = Auth::user();
        $grades = collect(); // TODO: Implement grades retrieval
        return view('grades.index', compact('student', 'grades'));
    }

    public function transcript()
    {
        $student = Auth::user();
        $transcript = collect(); // TODO: Implement transcript retrieval
        return view('grades.transcript', compact('student', 'transcript'));
    }
}
