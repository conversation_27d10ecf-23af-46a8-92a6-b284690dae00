/* Animation Styles */

/* Keyframe Animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes logoFloat {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes backgroundShift {
    0%, 100% {
        filter: hue-rotate(0deg);
    }
    25% {
        filter: hue-rotate(90deg);
    }
    50% {
        filter: hue-rotate(180deg);
    }
    75% {
        filter: hue-rotate(270deg);
    }
}

@keyframes techFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.06;
    }
    25% {
        transform: translateY(-20px) rotate(5deg);
        opacity: 0.1;
    }
    50% {
        transform: translateY(-10px) rotate(-3deg);
        opacity: 0.08;
    }
    75% {
        transform: translateY(-30px) rotate(8deg);
        opacity: 0.12;
    }
}

@keyframes academicFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg) scale(1);
        opacity: 0.06;
    }
    20% {
        transform: translateY(-15px) rotate(2deg) scale(1.05);
        opacity: 0.1;
    }
    40% {
        transform: translateY(-25px) rotate(-1deg) scale(0.95);
        opacity: 0.08;
    }
    60% {
        transform: translateY(-10px) rotate(3deg) scale(1.1);
        opacity: 0.12;
    }
    80% {
        transform: translateY(-20px) rotate(-2deg) scale(0.9);
        opacity: 0.09;
    }
}

@keyframes particleFloat {
    0% {
        transform: translateY(100vh) translateX(0px) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) translateX(100px) rotate(360deg);
        opacity: 0;
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes shakeX {
    from, to {
        transform: translate3d(0, 0, 0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translate3d(-10px, 0, 0);
    }
    20%, 40%, 60%, 80% {
        transform: translate3d(10px, 0, 0);
    }
}

@keyframes bounceIn {
    from, 20%, 40%, 60%, 80%, to {
        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    }
    0% {
        opacity: 0;
        transform: scale3d(.3, .3, .3);
    }
    20% {
        transform: scale3d(1.1, 1.1, 1.1);
    }
    40% {
        transform: scale3d(.9, .9, .9);
    }
    60% {
        opacity: 1;
        transform: scale3d(1.03, 1.03, 1.03);
    }
    80% {
        transform: scale3d(.97, .97, .97);
    }
    to {
        opacity: 1;
        transform: scale3d(1, 1, 1);
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale3d(.3, .3, .3);
    }
    50% {
        opacity: 1;
    }
}

@keyframes rotateIn {
    from {
        transform-origin: center;
        transform: rotate3d(0, 0, 1, -200deg);
        opacity: 0;
    }
    to {
        transform-origin: center;
        transform: none;
        opacity: 1;
    }
}

@keyframes slideInLeft {
    from {
        transform: translate3d(-100%, 0, 0);
        visibility: visible;
    }
    to {
        transform: translate3d(0, 0, 0);
    }
}

@keyframes slideInRight {
    from {
        transform: translate3d(100%, 0, 0);
        visibility: visible;
    }
    to {
        transform: translate3d(0, 0, 0);
    }
}

@keyframes heartbeat {
    0% {
        transform: scale(1);
    }
    14% {
        transform: scale(1.3);
    }
    28% {
        transform: scale(1);
    }
    42% {
        transform: scale(1.3);
    }
    70% {
        transform: scale(1);
    }
}

/* Animation Classes */
.animate__animated {
    animation-duration: 1s;
    animation-fill-mode: both;
}

.animate__fadeInDown {
    animation-name: fadeInDown;
}

.animate__shakeX {
    animation-name: shakeX;
}

.animate__bounceIn {
    animation-name: bounceIn;
}

.animate__zoomIn {
    animation-name: zoomIn;
}

.animate__rotateIn {
    animation-name: rotateIn;
}

.animate__slideInLeft {
    animation-name: slideInLeft;
}

.animate__slideInRight {
    animation-name: slideInRight;
}

.animate__pulse {
    animation-name: pulse;
    animation-iteration-count: infinite;
}

.animate__heartbeat {
    animation-name: heartbeat;
    animation-duration: 1.3s;
    animation-iteration-count: infinite;
}

/* Hover Animations */
.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.hover-scale {
    transition: transform 0.3s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-glow {
    transition: box-shadow 0.3s ease;
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.4);
}

/* Loading Animations */
.spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.dots-loading {
    display: inline-block;
}

.dots-loading::after {
    content: '';
    animation: dots 1.5s steps(4, end) infinite;
}

@keyframes dots {
    0%, 20% {
        content: '';
    }
    40% {
        content: '.';
    }
    60% {
        content: '..';
    }
    80%, 100% {
        content: '...';
    }
}

/* Transition Classes */
.transition-all {
    transition: all 0.3s ease;
}

.transition-colors {
    transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
}

.transition-transform {
    transition: transform 0.3s ease;
}

.transition-opacity {
    transition: opacity 0.3s ease;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
