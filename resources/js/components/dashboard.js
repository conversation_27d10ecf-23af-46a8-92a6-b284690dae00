// Dashboard specific JavaScript
document.addEventListener('DOMContentLoaded', function() {
    
    // Animate statistics cards on load
    const statsCards = document.querySelectorAll('.stats-card');
    statsCards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('animate-slide-in');
        }, index * 100);
    });
    
    // Animate chart containers
    const chartContainers = document.querySelectorAll('.chart-container');
    chartContainers.forEach((container, index) => {
        setTimeout(() => {
            container.classList.add('animate-fade-in');
        }, 500 + (index * 200));
    });
    
    // Quick action buttons hover effects
    const quickActions = document.querySelectorAll('.quick-action');
    quickActions.forEach(action => {
        action.addEventListener('mouseenter', function() {
            this.classList.add('scale-105');
        });
        
        action.addEventListener('mouseleave', function() {
            this.classList.remove('scale-105');
        });
    });
    
    // Auto-refresh dashboard data every 5 minutes
    setInterval(() => {
        refreshDashboardData();
    }, 300000); // 5 minutes
    
});

// Function to refresh dashboard data
function refreshDashboardData() {
    fetch('/dashboard/data')
        .then(response => response.json())
        .then(data => {
            // Update statistics if needed
            updateStatistics(data);
        })
        .catch(error => {
            console.log('Dashboard refresh error:', error);
        });
}

// Function to update statistics
function updateStatistics(data) {
    // Update student info
    if (data.student) {
        const nameElement = document.querySelector('[data-student-name]');
        if (nameElement) {
            nameElement.textContent = data.student.name;
        }
    }
    
    // Update counters with animation
    if (data.classes_count !== undefined) {
        animateCounter(document.querySelector('[data-classes-count]'), data.classes_count);
    }
    
    if (data.wallet_balance !== undefined) {
        animateCounter(document.querySelector('[data-wallet-balance]'), data.wallet_balance);
    }
}

// Function to animate counters
function animateCounter(element, targetValue) {
    if (!element) return;
    
    const currentValue = parseInt(element.textContent) || 0;
    const increment = (targetValue - currentValue) / 20;
    let current = currentValue;
    
    const timer = setInterval(() => {
        current += increment;
        if ((increment > 0 && current >= targetValue) || (increment < 0 && current <= targetValue)) {
            current = targetValue;
            clearInterval(timer);
        }
        element.textContent = Math.round(current);
    }, 50);
}
