/* Enhanced Login Page Styles */

/* Custom animations */
@keyframes float {
    0%, 100% { 
        transform: translateY(0px) rotate(0deg); 
    }
    33% { 
        transform: translateY(-20px) rotate(1deg); 
    }
    66% { 
        transform: translateY(-10px) rotate(-1deg); 
    }
}

@keyframes pulse-glow {
    0%, 100% { 
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.3), 0 0 40px rgba(147, 51, 234, 0.2); 
    }
    50% { 
        box-shadow: 0 0 40px rgba(59, 130, 246, 0.6), 0 0 80px rgba(147, 51, 234, 0.4); 
    }
}

@keyframes gradient-shift {
    0% { 
        background-position: 0% 50%; 
    }
    50% { 
        background-position: 100% 50%; 
    }
    100% { 
        background-position: 0% 50%; 
    }
}

@keyframes slide-in-up {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fade-in {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Animation classes */
.animate-float { 
    animation: float 6s ease-in-out infinite; 
}

.animate-pulse-glow { 
    animation: pulse-glow 3s ease-in-out infinite; 
}

.animate-gradient { 
    background-size: 200% 200%;
    animation: gradient-shift 8s ease infinite;
}

.animate-slide-in-up {
    animation: slide-in-up 0.6s ease-out;
}

.animate-fade-in {
    animation: fade-in 0.8s ease-out;
}

/* Glass morphism effects */
.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.dark .glass-effect {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Enhanced input styles */
.enhanced-input {
    background: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.enhanced-input:focus {
    background: rgba(255, 255, 255, 0.7);
    border-color: rgba(59, 130, 246, 0.5);
    box-shadow: 
        0 0 0 3px rgba(59, 130, 246, 0.1),
        0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.dark .enhanced-input {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .enhanced-input:focus {
    background: rgba(0, 0, 0, 0.4);
    border-color: rgba(59, 130, 246, 0.5);
}

/* Enhanced button styles */
.enhanced-button {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    border: none;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.enhanced-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.enhanced-button:hover::before {
    left: 100%;
}

.enhanced-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.enhanced-button:active {
    transform: translateY(0);
}

/* Loading spinner */
.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #ffffff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Feature cards */
.feature-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
}

.feature-card:hover {
    transform: translateY(-5px) scale(1.05);
}

/* Background patterns */
.bg-pattern {
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(147, 51, 234, 0.1) 0%, transparent 50%);
}

.dark .bg-pattern {
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(147, 51, 234, 0.05) 0%, transparent 50%);
}

/* Responsive adjustments */
@media (max-width: 1024px) {
    .glass-effect {
        backdrop-filter: blur(15px);
    }
}

@media (max-width: 768px) {
    .animate-float {
        animation-duration: 4s;
    }
    
    .glass-effect {
        backdrop-filter: blur(10px);
        margin: 1rem;
    }
}

@media (max-width: 480px) {
    .glass-effect {
        padding: 1.5rem;
        margin: 0.5rem;
    }
    
    .enhanced-input {
        padding: 0.75rem 1rem 0.75rem 3rem;
    }
}

/* Dark mode transitions */
.dark-mode-transition {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.3);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(59, 130, 246, 0.5);
}

/* Focus states for accessibility */
.enhanced-input:focus,
.enhanced-button:focus {
    outline: 2px solid rgba(59, 130, 246, 0.5);
    outline-offset: 2px;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    .animate-float,
    .animate-pulse-glow,
    .animate-gradient,
    .animate-slide-in-up,
    .animate-fade-in {
        animation: none;
    }
    
    .enhanced-button,
    .enhanced-input,
    .feature-card {
        transition: none;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .glass-effect {
        background: rgba(255, 255, 255, 0.9);
        border: 2px solid #000000;
    }
    
    .dark .glass-effect {
        background: rgba(0, 0, 0, 0.9);
        border: 2px solid #ffffff;
    }
}
