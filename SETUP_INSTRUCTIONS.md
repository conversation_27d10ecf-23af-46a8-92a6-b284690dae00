# Hướng dẫn thiết lập Student Portal

## 1. Database đã có sẵn

Hệ thống sử dụng database có sẵn với bảng `user` chứa dữ liệu thật của sinh viên.
Không cần chạy migration hay seeder.

## 2. Compile Assets

```bash
# Cài đặt dependencies
npm install

# Compile CSS và JS
npm run dev

# Hoặc watch cho development
npm run watch
```

## 3. Chạy ứng dụng

```bash
php artisan serve
```

## 4. Tài khoản sinh viên có sẵn

### Sinh viên (user_level = 3):
- **Phạm Văn <PERSON>ng:**
  - Email: `<EMAIL>`
  - Mã SV: `2025300232`
  - Mật khẩu: `123@123`

- **L<PERSON> <PERSON>h<PERSON>uỳnh Trang:**
  - Email: `<EMAIL>`
  - Mã SV: `2025300227`
  - <PERSON><PERSON><PERSON> khẩu: `123@123`

- **<PERSON><PERSON><PERSON>n <PERSON>t:**
  - Email: `<EMAIL>`
  - Mã SV: `2025300231`
  - <PERSON><PERSON><PERSON> khẩu: `123@123`

**Lưu ý:**
- Đăng nhập bằng email hoặc mã sinh viên + mật khẩu
- Mật khẩu mặc định `123@123` cho tài khoản chưa thiết lập mật khẩu
- Tài khoản có mật khẩu trong database sẽ sử dụng mật khẩu đó

## 5. Tính năng đăng nhập

- ✅ Đăng nhập bằng email hoặc mã sinh viên + mật khẩu
- ✅ Mật khẩu mặc định `123@123` cho tài khoản chưa có mật khẩu
- ✅ Chỉ sinh viên (user_level = 3) được phép đăng nhập
- ✅ Kết nối với database thật có sẵn
- ✅ Toggle hiển thị/ẩn mật khẩu
- ✅ Theme toggle (dark/light mode)
- ✅ Responsive design
- ✅ Validation và thông báo lỗi

## 6. Cấu trúc Database

### Bảng user (có sẵn):
- `id` - Primary key
- `user_login` - Mã đăng nhập (mã sinh viên)
- `user_email` - Email sinh viên
- `user_pass` - Mật khẩu (có thể trống)
- `user_level` - Cấp độ người dùng (1=Admin, 2=Teacher, 3=Student)
- `user_surname` - Họ
- `user_middlename` - Tên đệm
- `user_givenname` - Tên
- `user_code` - Mã sinh viên
- `user_DOB` - Ngày sinh
- `user_address` - Địa chỉ
- `user_telephone` - Số điện thoại
- Và nhiều trường khác...

## 7. Middleware

- `CheckUserLevel` - Kiểm tra cấp độ người dùng
- Sử dụng: `Route::middleware(['auth', 'user.level:3'])`

## 8. Troubleshooting

### Lỗi migration:
```bash
php artisan migrate:fresh --seed
```

### Lỗi assets:
```bash
npm run dev
```

### Lỗi permission:
```bash
chmod -R 775 storage bootstrap/cache
```
