<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class WalletController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index()
    {
        $student = Auth::user();
        $wallet = null; // TODO: Implement wallet retrieval
        $transactions = collect(); // TODO: Implement transactions retrieval
        return view('wallet.index', compact('student', 'wallet', 'transactions'));
    }

    public function topup()
    {
        $student = Auth::user();
        return view('wallet.topup', compact('student'));
    }

    public function history()
    {
        $student = Auth::user();
        $transactions = collect(); // TODO: Implement transaction history
        return view('wallet.history', compact('student', 'transactions'));
    }
}
