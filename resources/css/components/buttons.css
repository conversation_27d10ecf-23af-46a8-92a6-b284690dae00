/* Button Component Styles */

/* Base Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 14px 24px;
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    user-select: none;
    white-space: nowrap;
    min-height: 48px;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn i {
    margin-right: 8px;
    font-size: 0.9rem;
}

.btn:last-child i,
.btn i:only-child {
    margin-right: 0;
}

/* Primary Button */
.btn-primary {
    background: var(--primary-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #7c8ef0 0%, #8a5fb8 100%);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* Secondary Button */
.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Success Button */
.btn-success {
    background: var(--success-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(0, 210, 255, 0.3);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 210, 255, 0.4);
    background: linear-gradient(135deg, #1ae5ff 0%, #4f8bdb 100%);
}

/* Danger Button */
.btn-danger {
    background: var(--accent-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
    background: linear-gradient(135deg, #ff8181 0%, #f46b2a 100%);
}

/* Warning Button */
.btn-warning {
    background: linear-gradient(135deg, #ffd93d 0%, #ffed4e 100%);
    color: #2d3748;
    box-shadow: 0 4px 15px rgba(255, 217, 61, 0.3);
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 217, 61, 0.4);
    background: linear-gradient(135deg, #ffe066 0%, #fff177 100%);
}

/* Info Button */
.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
}

.btn-info:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(23, 162, 184, 0.4);
    background: linear-gradient(135deg, #1fb3d3 0%, #2dd4aa 100%);
}

/* Light Button */
.btn-light {
    background: rgba(255, 255, 255, 0.9);
    color: #2d3748;
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
}

.btn-light:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
}

/* Dark Button */
.btn-dark {
    background: var(--dark-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.btn-dark:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

/* Outline Buttons */
.btn-outline-primary {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
}

.btn-outline-primary:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-outline-secondary {
    background: transparent;
    color: var(--text-secondary);
    border: 2px solid var(--text-secondary);
}

.btn-outline-secondary:hover {
    background: var(--text-secondary);
    color: var(--glass-bg);
    transform: translateY(-2px);
}

/* Google Button */
.btn-google {
    background: rgba(255, 255, 255, 0.95);
    color: #2d3748;
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    font-weight: 500;
}

.btn-google:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    color: #2d3748;
}

.google-icon {
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Button Sizes */
.btn-sm {
    padding: 8px 16px;
    font-size: 0.875rem;
    min-height: 36px;
    border-radius: 8px;
}

.btn-lg {
    padding: 18px 32px;
    font-size: 1.125rem;
    min-height: 56px;
    border-radius: 14px;
}

.btn-xl {
    padding: 22px 40px;
    font-size: 1.25rem;
    min-height: 64px;
    border-radius: 16px;
}

/* Button Widths */
.btn-block,
.w-100 {
    width: 100%;
}

/* Button Groups */
.btn-group {
    display: flex;
    gap: 8px;
}

.btn-group .btn {
    flex: 1;
}

.btn-group-vertical {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.btn-group-vertical .btn {
    width: 100%;
}

/* Icon Buttons */
.btn-icon {
    width: 48px;
    height: 48px;
    padding: 0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-icon i {
    margin: 0;
    font-size: 1.2rem;
}

.btn-icon.btn-sm {
    width: 36px;
    height: 36px;
}

.btn-icon.btn-lg {
    width: 56px;
    height: 56px;
}

/* Loading State */
.btn-loading {
    position: relative;
    color: transparent !important;
}

.btn-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    color: white;
}

/* Floating Action Button */
.btn-fab {
    position: fixed;
    bottom: 24px;
    right: 24px;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
}

.btn-fab:hover {
    transform: scale(1.1);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.4);
}

/* Button Animations */
.btn-pulse {
    animation: pulse 2s infinite;
}

.btn-bounce {
    animation: bounceIn 1s;
}

/* Responsive Design */
@media (max-width: 768px) {
    .btn {
        padding: 12px 20px;
        font-size: 0.95rem;
        min-height: 44px;
    }
    
    .btn-sm {
        padding: 6px 12px;
        font-size: 0.8rem;
        min-height: 32px;
    }
    
    .btn-lg {
        padding: 16px 28px;
        font-size: 1.1rem;
        min-height: 52px;
    }
    
    .btn-group {
        flex-direction: column;
        gap: 6px;
    }
    
    .btn-fab {
        bottom: 20px;
        right: 20px;
        width: 52px;
        height: 52px;
    }
}

@media (max-width: 480px) {
    .btn {
        padding: 10px 16px;
        font-size: 0.9rem;
        min-height: 40px;
        border-radius: 10px;
    }
    
    .btn i {
        margin-right: 6px;
        font-size: 0.85rem;
    }
    
    .btn-fab {
        width: 48px;
        height: 48px;
    }
}
