/* Card Component Styles */

/* Base Card */
.card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    box-shadow: var(--shadow-soft);
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-hover);
    border-color: rgba(255, 255, 255, 0.15);
}

/* Card Header */
.card-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--glass-border);
    background: rgba(255, 255, 255, 0.02);
}

.card-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 8px;
}

.card-subtitle {
    margin: 4px 0 0 0;
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 400;
}

/* Card Body */
.card-body {
    padding: 24px;
}

.card-body p:last-child {
    margin-bottom: 0;
}

/* Card Footer */
.card-footer {
    padding: 16px 24px;
    border-top: 1px solid var(--glass-border);
    background: rgba(255, 255, 255, 0.02);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* Card Text */
.card-text {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 16px;
}

.card-text:last-child {
    margin-bottom: 0;
}

/* Alert Cards */
.alert {
    padding: 16px 20px;
    border-radius: 12px;
    border: 1px solid transparent;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
    backdrop-filter: blur(10px);
    animation: slideInDown 0.5s ease-out;
}

.alert i {
    font-size: 1.1rem;
    flex-shrink: 0;
}

.alert-success {
    background: rgba(40, 167, 69, 0.15);
    border-color: rgba(40, 167, 69, 0.3);
    color: #28a745;
}

.alert-danger {
    background: rgba(220, 53, 69, 0.15);
    border-color: rgba(220, 53, 69, 0.3);
    color: #ff6b6b;
}

.alert-warning {
    background: rgba(255, 193, 7, 0.15);
    border-color: rgba(255, 193, 7, 0.3);
    color: #ffc107;
}

.alert-info {
    background: rgba(23, 162, 184, 0.15);
    border-color: rgba(23, 162, 184, 0.3);
    color: #17a2b8;
}

/* Stats Cards */
.stats-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    padding: 24px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--accent-gradient);
}

.stats-card:hover {
    transform: translateY(-6px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 8px;
    display: block;
}

.stats-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

.stats-icon {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 2rem;
    color: rgba(255, 107, 107, 0.3);
}

/* Feature Cards */
.feature-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 32px 24px;
    text-align: center;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, rgba(255, 107, 107, 0.1), transparent);
    animation: rotate 10s linear infinite;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.feature-card:hover::before {
    opacity: 1;
}

.feature-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 107, 107, 0.3);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: var(--accent-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 2rem;
    color: white;
    box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
    position: relative;
    z-index: 2;
}

.feature-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 12px;
    position: relative;
    z-index: 2;
}

.feature-description {
    color: var(--text-secondary);
    line-height: 1.6;
    position: relative;
    z-index: 2;
}

/* Profile Cards */
.profile-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 32px 24px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
}

.profile-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    margin: 0 auto 20px;
    border: 4px solid var(--glass-border);
    overflow: hidden;
    position: relative;
}

.profile-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-name {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.profile-role {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 20px;
}

.profile-stats {
    display: flex;
    justify-content: space-around;
    margin-top: 24px;
    padding-top: 24px;
    border-top: 1px solid var(--glass-border);
}

.profile-stat {
    text-align: center;
}

.profile-stat-number {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    display: block;
}

.profile-stat-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Dividers */
.auth-divider {
    display: flex;
    align-items: center;
    margin: 24px 0;
    gap: 16px;
}

.divider-line {
    flex: 1;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--glass-border), transparent);
}

.divider-text {
    color: var(--text-secondary);
    font-size: 0.85rem;
    font-weight: 500;
    padding: 0 8px;
    background: var(--glass-bg);
    border-radius: 20px;
    white-space: nowrap;
}

/* Card Animations */
@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .card-header,
    .card-body,
    .card-footer {
        padding: 16px 20px;
    }
    
    .stats-card,
    .feature-card,
    .profile-card {
        padding: 24px 20px;
    }
    
    .feature-icon {
        width: 70px;
        height: 70px;
        font-size: 1.75rem;
    }
    
    .profile-avatar {
        width: 80px;
        height: 80px;
    }
    
    .stats-number {
        font-size: 2rem;
    }
    
    .profile-stats {
        flex-direction: column;
        gap: 16px;
    }
}

@media (max-width: 480px) {
    .card {
        border-radius: 12px;
    }
    
    .card-header,
    .card-body,
    .card-footer {
        padding: 12px 16px;
    }
    
    .alert {
        padding: 12px 16px;
        border-radius: 10px;
    }
    
    .stats-card,
    .feature-card,
    .profile-card {
        padding: 20px 16px;
        border-radius: 16px;
    }
    
    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
    
    .profile-avatar {
        width: 70px;
        height: 70px;
    }
}
