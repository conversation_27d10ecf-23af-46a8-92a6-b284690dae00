<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSubjectsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('subjects', function (Blueprint $table) {
            $table->id();
            $table->string('subject_code', 20)->unique(); // Mã môn học (VD: IT101)
            $table->string('subject_name'); // Tên môn học
            $table->text('description')->nullable(); // Mô tả môn học
            $table->integer('credits')->default(3); // Số tín chỉ
            $table->integer('theory_hours')->default(0); // Số giờ lý thuyết
            $table->integer('practice_hours')->default(0); // Số giờ thực hành
            $table->json('prerequisites')->nullable(); // Môn học tiên quyết (JSON array)
            $table->string('department')->nullable(); // Khoa/bộ môn
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->timestamps();

            $table->index(['subject_code', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('subjects');
    }
}
